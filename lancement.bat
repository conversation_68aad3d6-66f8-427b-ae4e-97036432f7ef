@echo off
echo.
echo ========================================
echo   EXTRACTEUR DE VOIX PEPE v2.0
echo ========================================
echo.

cd /D H:\IA\speechbrain\song

call H:\Anaconda\Scripts\activate.bat
conda activate H:\IA\speechbrain\venv

echo Choisissez votre version:
echo.
echo [1] Version classique (extract_voice.py)
echo [2] Version amelioree (extract_voice_improved.py)
echo [3] Configuration avancee
echo [4] Benchmark des presets
echo [5] Quitter
echo.

set /p choice="Votre choix (1-5): "

if "%choice%"=="1" (
    echo Lancement de la version classique...
    python extract_voice.py
) else if "%choice%"=="2" (
    echo Lancement de la version amelioree...
    python extract_voice_improved.py
) else if "%choice%"=="3" (
    echo Creation des fichiers de configuration...
    python config_advanced.py
) else if "%choice%"=="4" (
    echo Lancement du benchmark...
    echo Assurez-vous d'avoir configure les chemins dans benchmark_tool.py
    python benchmark_tool.py --help
) else if "%choice%"=="5" (
    echo Au revoir!
    exit /b 0
) else (
    echo Choix invalide. Lancement de la version amelioree par defaut...
    python extract_voice_improved.py
)

echo.
echo Traitement termine!
pause