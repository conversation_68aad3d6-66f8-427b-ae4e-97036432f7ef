@echo off
echo.
echo ========================================
echo   EXTRACTEUR DE VOIX PEPE v2.0
echo ========================================
echo.

cd /D H:\IA\speechbrain\song

echo Activation de l'environnement conda...
call conda run -n H:\IA\speechbrain\venv python -c "print('Environnement pret!')"

:menu
echo.
echo Choisissez votre version:
echo.
echo [1] Version classique (extract_voice.py)
echo [2] Version amelioree (extract_voice_improved.py)
echo [3] Configuration avancee
echo [4] Benchmark des presets
echo [5] Quitter
echo.

set /p choice="Votre choix (1-5): "

if "%choice%"=="1" (
    echo.
    echo Lancement de la version classique...
    call conda run -n H:\IA\speechbrain\venv python extract_voice.py
    goto end
) else if "%choice%"=="2" (
    echo.
    echo Lancement de la version amelioree...
    call conda run -n H:\IA\speechbrain\venv python extract_voice_improved.py
    goto end
) else if "%choice%"=="3" (
    echo.
    echo Creation des fichiers de configuration...
    call conda run -n H:\IA\speechbrain\venv python config_advanced.py
    echo.
    echo Fichiers de configuration crees! Voulez-vous continuer?
    pause
    goto menu
) else if "%choice%"=="4" (
    echo.
    echo Lancement du benchmark...
    echo Exemple d'utilisation:
    echo python benchmark_tool.py --target CibleGohan.wav --input input --output benchmark_results
    echo.
    call conda run -n H:\IA\speechbrain\venv python benchmark_tool.py --help
    echo.
    pause
    goto menu
) else if "%choice%"=="5" (
    echo Au revoir!
    goto end
) else (
    echo.
    echo Choix invalide. Veuillez choisir entre 1 et 5.
    goto menu
)

:end
echo.
echo Traitement termine!
pause