# ############################################################################
# Model: E2E ASR with Transformer
# Encoder: Conformer Encoder
# Decoder: Transformer Decoder + (CTC/ATT joint) beamsearch + TransformerLM
# Tokens: unigram
# losses: CTC + KLdiv (Label Smoothing loss)
# Training: KsponSpeech 965.2h
# Authors: <AUTHORS>
# ############################################################################
# Seed needs to be set at top of yaml, before objects with parameters are made
seed: 7775
__set_seed: !apply:torch.manual_seed [!ref <seed>]
output_folder: !ref results/conformer/<seed>
output_wer_folder: !ref <output_folder>/
save_folder: !ref <output_folder>/save
train_log: !ref <output_folder>/train_log.txt

# Language model (LM) pretraining
# NB: To avoid mismatch, the speech recognizer must be trained with the same
# tokenizer used for LM training.
pretrained_lm_tokenizer_path: ddwkim/asr-conformer-transformerlm-ksponspeech

# Data files
data_folder: !PLACEHOLDER # e.g., /path/to/KsponSpeech
train_splits: ["train"]
dev_splits: ["dev"]
test_splits: ["eval_clean", "eval_other"]
skip_prep: False
train_csv: !ref <data_folder>/train.csv
valid_csv: !ref <data_folder>/dev.csv
test_csv:
    - !ref <data_folder>/eval_clean.csv
    - !ref <data_folder>/eval_other.csv

ckpt_interval_minutes: 30 # save checkpoint every N min

####################### Training Parameters ####################################
# To make Transformers converge, the global bath size should be large enough.
# The global batch size is computed as batch_size * n_gpus * grad_accumulation_factor.
# Empirically, we found that this value should be >= 128.
# Please, set your parameters accordingly.
number_of_epochs: 60
batch_size: 48 # This works for 1x GPU with 80GB
ctc_weight: 0.4
grad_accumulation_factor: 2
max_grad_norm: 5.0
loss_reduction: 'batchmean'
sorting: random
avg_checkpoints: 5 # Number of checkpoints to average for evaluation

dynamic_batching: False

dynamic_batch_sampler:
    feats_hop_size: 0.01
    max_batch_len: 100000 # in terms of frames
    num_buckets: 200
    shuffle_ex: False # if true re-creates batches at each epoch shuffling examples.
    batch_ordering: random
    max_batch_ex: -1

# stages related parameters
lr_adam: 0.001

# Feature parameters
sample_rate: 16000
n_fft: 400
n_mels: 80

# Dataloader options
train_dataloader_opts:
    batch_size: !ref <batch_size>
    shuffle: True
    num_workers: 16

valid_dataloader_opts:
    batch_size: 1

test_dataloader_opts:
    batch_size: 1

####################### Model Parameters ###########################
# Transformer
d_model: 256
nhead: 4
num_encoder_layers: 12
num_decoder_layers: 6
d_ffn: 2048
transformer_dropout: 0.1
activation: !name:torch.nn.GELU
output_neurons: 5000

# Outputs
blank_index: 0
label_smoothing: 0.0
pad_index: 0
bos_index: 1
eos_index: 2

# Decoding parameters
min_decode_ratio: 0.0
max_decode_ratio: 1.0
valid_search_interval: 10
valid_beam_size: 10
test_beam_size: 60
lm_weight: 0.25
ctc_weight_decode: 0.60

############################## models ################################

CNN: !new:speechbrain.lobes.models.convolution.ConvolutionFrontEnd
    input_shape: (8, 10, 80)
    num_blocks: 3
    num_layers_per_block: 1
    out_channels: (64, 64, 64)
    kernel_sizes: (5, 5, 1)
    strides: (2, 2, 1)
    residuals: (False, False, True)

Transformer: !new:speechbrain.lobes.models.transformer.TransformerASR.TransformerASR # yamllint disable-line rule:line-length
    input_size: 1280
    tgt_vocab: !ref <output_neurons>
    d_model: !ref <d_model>
    nhead: !ref <nhead>
    num_encoder_layers: !ref <num_encoder_layers>
    num_decoder_layers: !ref <num_decoder_layers>
    d_ffn: !ref <d_ffn>
    dropout: !ref <transformer_dropout>
    activation: !ref <activation>
    encoder_module: conformer
    attention_type: RelPosMHAXL
    normalize_before: True
    causal: False

# This is the TransformerLM that is used according to the Huggingface repository
# Visit the HuggingFace model corresponding to the pretrained_lm_tokenizer_path
# For more details about the model!
# NB: It has to match the pre-trained TransformerLM!!
lm_model: !new:speechbrain.lobes.models.transformer.TransformerLM.TransformerLM # yamllint disable-line rule:line-length
    vocab: !ref <output_neurons>
    d_model: 768
    nhead: 12
    num_encoder_layers: 12
    num_decoder_layers: 0
    d_ffn: 3072
    dropout: 0.0
    activation: !name:torch.nn.GELU
    normalize_before: False

tokenizer: !new:sentencepiece.SentencePieceProcessor

ctc_lin: !new:speechbrain.nnet.linear.Linear
    input_size: !ref <d_model>
    n_neurons: !ref <output_neurons>

seq_lin: !new:speechbrain.nnet.linear.Linear
    input_size: !ref <d_model>
    n_neurons: !ref <output_neurons>

modules:
    CNN: !ref <CNN>
    Transformer: !ref <Transformer>
    seq_lin: !ref <seq_lin>
    ctc_lin: !ref <ctc_lin>
    normalize: !ref <normalize>

model: !new:torch.nn.ModuleList
    - [!ref <CNN>, !ref <Transformer>, !ref <seq_lin>, !ref <ctc_lin>]

# define two optimizers here for two-stage training
Adam: !name:torch.optim.Adam
    lr: !ref <lr_adam>
    betas: (0.9, 0.98)
    eps: 0.000000001


# Scorer
ctc_scorer: !new:speechbrain.decoders.scorer.CTCScorer
    eos_index: !ref <eos_index>
    blank_index: !ref <blank_index>
    ctc_fc: !ref <ctc_lin>

transformerlm_scorer: !new:speechbrain.decoders.scorer.TransformerLMScorer
    language_model: !ref <lm_model>
    temperature: 1.30

valid_scorer: !new:speechbrain.decoders.scorer.ScorerBuilder
    full_scorers: [!ref <ctc_scorer>]
    weights:
        ctc: !ref <ctc_weight_decode>

test_scorer: !new:speechbrain.decoders.scorer.ScorerBuilder
    full_scorers: [!ref <transformerlm_scorer>,
                   !ref <ctc_scorer>]
    weights:
        transformerlm: !ref <lm_weight>
        ctc: !ref <ctc_weight_decode>

valid_search: !new:speechbrain.decoders.S2STransformerBeamSearcher
    modules: [!ref <Transformer>, !ref <seq_lin>]
    bos_index: !ref <bos_index>
    eos_index: !ref <eos_index>
    min_decode_ratio: !ref <min_decode_ratio>
    max_decode_ratio: !ref <max_decode_ratio>
    beam_size: !ref <valid_beam_size>
    using_eos_threshold: False
    length_normalization: True
    scorer: !ref <valid_scorer>

test_search: !new:speechbrain.decoders.S2STransformerBeamSearcher
    modules: [!ref <Transformer>, !ref <seq_lin>]
    bos_index: !ref <bos_index>
    eos_index: !ref <eos_index>
    min_decode_ratio: !ref <min_decode_ratio>
    max_decode_ratio: !ref <max_decode_ratio>
    beam_size: !ref <test_beam_size>
    temperature: 1.30
    using_eos_threshold: False
    length_normalization: True
    scorer: !ref <test_scorer>

log_softmax: !new:torch.nn.LogSoftmax
    dim: -1

ctc_cost: !name:speechbrain.nnet.losses.ctc_loss
    blank_index: !ref <blank_index>
    reduction: !ref <loss_reduction>

seq_cost: !name:speechbrain.nnet.losses.kldiv_loss
    label_smoothing: !ref <label_smoothing>
    reduction: !ref <loss_reduction>

noam_annealing: !new:speechbrain.nnet.schedulers.NoamScheduler
    lr_initial: !ref <lr_adam>
    n_warmup_steps: 25000
    #model_size: !ref <d_model>

checkpointer: !new:speechbrain.utils.checkpoints.Checkpointer
    checkpoints_dir: !ref <save_folder>
    recoverables:
        model: !ref <model>
        noam_scheduler: !ref <noam_annealing>
        normalizer: !ref <normalize>
        counter: !ref <epoch_counter>

epoch_counter: !new:speechbrain.utils.epoch_loop.EpochCounter
    limit: !ref <number_of_epochs>

normalize: !new:speechbrain.processing.features.InputNormalization
    norm_type: global
    update_until_epoch: 4

# Time Drop
time_drop_length_low: 15  # Min length for temporal chunk to drop in spectrogram
time_drop_length_high: 25  # Max length for temporal chunk to drop in spectrogram
time_drop_count_low: 5  # Min number of chunks to drop in time in the spectrogram
time_drop_count_high: 5  # Max number of chunks to drop in time in the spectrogram
time_drop_replace: "zeros"  # Method of dropping chunks

time_drop: !new:speechbrain.augment.freq_domain.SpectrogramDrop
    drop_length_low: !ref <time_drop_length_low>
    drop_length_high: !ref <time_drop_length_high>
    drop_count_low: !ref <time_drop_count_low>
    drop_count_high: !ref <time_drop_count_high>
    replace: !ref <time_drop_replace>
    dim: 1

# Frequency Drop
freq_drop_length_low: 25  # Min length for chunks to drop in frequency in the spectrogram
freq_drop_length_high: 35  # Max length for chunks to drop in frequency in the spectrogram
freq_drop_count_low: 2  # Min number of chunks to drop in frequency in the spectrogram
freq_drop_count_high: 2  # Max number of chunks to drop in frequency in the spectrogram
freq_drop_replace: "zeros"  # Method of dropping chunks

freq_drop: !new:speechbrain.augment.freq_domain.SpectrogramDrop
    drop_length_low: !ref <freq_drop_length_low>
    drop_length_high: !ref <freq_drop_length_high>
    drop_count_low: !ref <freq_drop_count_low>
    drop_count_high: !ref <freq_drop_count_high>
    replace: !ref <freq_drop_replace>
    dim: 2

# Time warp
time_warp_window: 5  # Length of time warping window
time_warp_mode: "bicubic"  # Time warping method

time_warp: !new:speechbrain.augment.freq_domain.Warping
    warp_window: !ref <time_warp_window>
    warp_mode: !ref <time_warp_mode>
    dim: 1

fea_augment: !new:speechbrain.augment.augmenter.Augmenter
    parallel_augment: False
    concat_original: False
    repeat_augment: 1
    shuffle_augmentations: False
    min_augmentations: 3
    max_augmentations: 3
    augment_prob: 1.0
    augmentations: [
        !ref <time_drop>,
        !ref <freq_drop>,
        !ref <time_warp>]

# Speed perturbation
do_speed_perturb: True
speed_changes: [95, 100, 105]  # List of speed changes for time-stretching

speed_perturb: !new:speechbrain.augment.time_domain.SpeedPerturb
    orig_freq: !ref <sample_rate>
    speeds: !ref <speed_changes>

compute_features: !new:speechbrain.lobes.features.Fbank
    sample_rate: !ref <sample_rate>
    n_fft: !ref <n_fft>
    n_mels: !ref <n_mels>

train_logger: !new:speechbrain.utils.train_logger.FileTrainLogger
    save_file: !ref <train_log>

error_rate_computer: !name:speechbrain.utils.metric_stats.ErrorRateStats
acc_computer: !name:speechbrain.utils.Accuracy.AccuracyStats

# The pretrainer allows a mapping between pretrained files and instances that
# are declared in the yaml. E.g here, we will download the file lm.ckpt
# and it will be loaded into "lm" which is pointing to the <lm_model> defined
# before.
pretrainer: !new:speechbrain.utils.parameter_transfer.Pretrainer
    collect_in: !ref <save_folder>
    loadables:
        lm: !ref <lm_model>
        tokenizer: !ref <tokenizer>
    paths:
        lm: !ref <pretrained_lm_tokenizer_path>/lm.ckpt
        tokenizer: !ref <pretrained_lm_tokenizer_path>/tokenizer.ckpt
