# ########################################
# Emotion recognition from speech using ECAPA-TDNN
# (smaller version with layer sizes and number
# of attention heads divided by 2 compared to original)
#
#  * Authors: <AUTHORS>
#  * Modified by <PERSON><PERSON><PERSON>
# ########################################

# Seed needs to be set at top of yaml, before objects with parameters are made
seed: 1968
__set_seed: !apply:torch.manual_seed [!ref <seed>]

# Dataset will be downloaded to the `data_original`
data_folder: !PLACEHOLDER  # e.g., /path/to/IEMOCAP_full_release
output_folder: !ref results/ECAPA-TDNN/<seed>
save_folder: !ref <output_folder>/save
train_log: !ref <output_folder>/train_log.txt


# different speakers for train, valid and test sets
different_speakers: False
# which speaker is used for test set, value from 1 to 10
test_spk_id: 1

# Path where data manifest files will be stored
train_annotation: !ref <output_folder>/train.json
valid_annotation: !ref <output_folder>/valid.json
test_annotation: !ref <output_folder>/test.json
split_ratio: [80, 10, 10]
skip_prep: False

# The train logger writes training statistics to a file, as well as stdout.
train_logger: !new:speechbrain.utils.train_logger.FileTrainLogger
    save_file: !ref <train_log>

error_stats: !name:speechbrain.utils.metric_stats.MetricStats
    metric: !name:speechbrain.nnet.losses.classification_error
        reduction: batch

ckpt_interval_minutes: 15 # save checkpoint every N min

# Training Parameters
number_of_epochs: 30
batch_size: 16
grad_accumulation_factor: 2
lr: 0.0001
weight_decay: 0.00002
base_lr: 0.000001
max_lr: !ref <lr>
step_size: 1088  # 4 times number of iterations/epoch (2 to 10 is suggested)
mode: exp_range # mode of learning rate schedule (triangular, triangular2, exp_range)
gamma: 0.9998  # divides learning rate by 3 over the first 20 epochs
# sample_rate: 16000
shuffle: True
# random_chunk: True
drop_last: False

# Feature parameters
n_mels: 80
left_frames: 0
right_frames: 0
deltas: False

# Number of emotions
out_n_neurons: 4 # (anger, happiness, sadness, neutral)

dataloader_options:
    batch_size: !ref <batch_size>
    shuffle: !ref <shuffle>
    num_workers: 2  # 2 on linux but 0 works on windows
    drop_last: !ref <drop_last>

# Functions
compute_features: !new:speechbrain.lobes.features.Fbank
    n_mels: !ref <n_mels>
    left_frames: !ref <left_frames>
    right_frames: !ref <right_frames>
    deltas: !ref <deltas>

embedding_model: !new:speechbrain.lobes.models.ECAPA_TDNN.ECAPA_TDNN
    input_size: !ref <n_mels>
    channels: [512, 512, 512, 512, 1536]
    kernel_sizes: [5, 3, 3, 3, 1]
    dilations: [1, 2, 3, 4, 1]
    attention_channels: 64
    lin_neurons: 96

classifier: !new:speechbrain.lobes.models.ECAPA_TDNN.Classifier
    input_size: 96
    out_neurons: !ref <out_n_neurons>

epoch_counter: !new:speechbrain.utils.epoch_loop.EpochCounter
    limit: !ref <number_of_epochs>

mean_var_norm: !new:speechbrain.processing.features.InputNormalization
    norm_type: sentence
    std_norm: False

modules:
    compute_features: !ref <compute_features>
    embedding_model: !ref <embedding_model>
    classifier: !ref <classifier>
    mean_var_norm: !ref <mean_var_norm>

compute_cost: !new:speechbrain.nnet.losses.LogSoftmaxWrapper
    loss_fn: !new:speechbrain.nnet.losses.AdditiveAngularMargin
        margin: 0.2
        scale: 30

# compute_error: !name:speechbrain.nnet.losses.classification_error

opt_class: !name:torch.optim.Adam
    lr: !ref <lr>
    weight_decay: !ref <weight_decay>

lr_annealing: !new:speechbrain.nnet.schedulers.CyclicLRScheduler
    mode: !ref <mode>
    gamma: !ref <gamma>
    base_lr: !ref <base_lr>
    max_lr: !ref <max_lr>
    step_size: !ref <step_size>

checkpointer: !new:speechbrain.utils.checkpoints.Checkpointer
    checkpoints_dir: !ref <save_folder>
    recoverables:
        embedding_model: !ref <embedding_model>
        classifier: !ref <classifier>
        normalizer: !ref <mean_var_norm>
        counter: !ref <epoch_counter>
