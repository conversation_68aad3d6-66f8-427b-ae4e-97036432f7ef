{# The :autogenerated: tag is picked up by breadcrumbs.html to suppress "Edit on Github" link #}
:autogenerated:

{{ fullname }}
{% for item in range(fullname|length) -%}={%- endfor %}

.. automodule:: {{ fullname }}
    {% if members -%}
    :members: {{ members|join(", ") }}
    :undoc-members:
    :show-inheritance:
    {%- endif %}

{% if submodules %}
    .. toctree::
       :hidden:
       :maxdepth: 1
{% for item in submodules %}
       {{ fullname }}.{{ item }}
{%- endfor %}

    .. autosummary::

{% for item in submodules %}
       {{ fullname }}.{{ item }}
{%- endfor %}
{%- endif -%}



{% if subpackages %}
    .. toctree::
       :hidden:
       :maxdepth: 1
{% for item in subpackages %}
       {{ fullname }}.{{ item }}
       {%- endfor %}

    .. autosummary::

{% for item in subpackages %}
       {{ fullname }}.{{ item }}
{%- endfor %}
{%- endif %}

{% set all = get_members(in_list='__all__', include_imported=True) %}
{% if members or all %}
    Summary
    -------

{%- set exceptions = get_members(typ='exception', in_list='__all__', include_imported=True, out_format='table') -%}
{%- set classes = get_members(typ='class', in_list='__all__', include_imported=True, out_format='table') -%}
{%- set functions = get_members(typ='function', in_list='__all__', include_imported=True, out_format='table') -%}
{%- set data = get_members(typ='data', in_list='__all__', include_imported=True, out_format='table') -%}
{%- set private_exceptions = get_members(typ='exception', in_list='__private__', out_format='table') -%}
{%- set private_classes = get_members(typ='class', in_list='__private__', out_format='table') -%}
{%- set private_functions = get_members(typ='function', in_list='__private__', out_format='table') -%}

    {%- if exceptions %}

    ``__all__`` Exceptions:

{% for line in exceptions %}
    {{ line }}
{%- endfor %}
    {%- endif %}
    {%- if private_exceptions %}

    Private Exceptions:

{% for line in private_exceptions %}
    {{ line }}
{%- endfor %}
    {%- endif %}

    {%- if classes %}

    ``__all__`` Classes:

{% for line in classes %}
    {{ line }}
{%- endfor %}
    {%- endif %}
    {%- if private_classes %}

    Private Classes:

{% for line in private_classes %}
    {{ line }}
{%- endfor %}
    {%- endif %}

    {%- if functions %}

    ``__all__`` Functions:

{% for line in functions %}
    {{ line }}
{%- endfor %}
    {%- endif %}
    {%- if private_functions %}

    Private Functions:

{% for line in private_functions %}
    {{ line }}
{%- endfor %}
    {%- endif %}

    {%- if data %}

    ``__all__`` Data:

{% for line in data %}
    {{ line }}
{%- endfor %}
    {%- endif %}

{%- endif %}


{% if members %}
    Reference
    ---------

{%- endif %}
