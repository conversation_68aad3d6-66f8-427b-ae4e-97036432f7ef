# yamllint disable
################################
# Configuration for generating Noisy Speech Dataset
# - sampling_rate: Specify the sampling rate. Default is 16 kHz
# - audioformat: default is .wav
# - audio_length: Minimum Length of each audio clip (noisy and clean speech)
#   in seconds that will be generated by augmenting utterances.
# - silence_length: Duration of silence introduced between clean speech
#   utterances.
# - total_hours: Total number of hours of data required. Units are in hours.
# - snr_lower: Lower bound for SNR required (default: 0 dB)
# - snr_upper: Upper bound for SNR required (default: 40 dB)
# - target_level_lower: Lower bound for the target audio level
#   before audiowrite (default: -35 dB)
# - target_level_upper: Upper bound for the target audio level
#   before audiowrite (default: -15 dB)
# - total_snrlevels: Number of SNR levels required (default: 5, which means
#   there are 5 levels between snr_lower and snr_upper)
# - clean_activity_threshold: Activity threshold for clean speech
# - noise_activity_threshold: Activity threshold for noise
# - fileindex_start: Starting file ID that will be used in filenames
# - fileindex_end: Last file ID that will be used in filenames
# - is_test_set: Set it to True if it is the test set, else False for the
# - log_dir: Specify path to the directory to store all the log files
# ################################
# yamllint enable


# Data storage params
input_shards_dir: !PLACEHOLDER  #  ../DNS-shards
split_name: !PLACEHOLDER # read_speech, german_speech, italian_speech, french_speech etc
rirs: RIR_table_simple.csv

# Noisy data synthesis params
sampling_rate: 16000 # sampling rate of synthesized signal
audioformat: "*.wav"
audio_length: 4
silence_length: 0.2
total_hours: 100
snr_lower: -5
snr_upper: 15
randomize_snr: True
target_level_lower: -35
target_level_upper: -15
total_snrlevels: 21
clean_activity_threshold: 0.6
noise_activity_threshold: 0.0
fileindex_start: None
fileindex_end: None
is_test_set: False

# Source dir
rir_table_csv: !ref <rirs>

# Directory path where Webdatasets of DNS clean and noise shards are located.
input_sampling_rate: 48000  # sampling rate of input signal
clean_meta: !ref <input_shards_dir>/clean_fullband/<split_name>/meta.json
noise_meta: !ref <input_shards_dir>/noise_fullband/meta.json
clean_fullband_shards: !ref <input_shards_dir>/clean_fullband/<split_name>/shard-{000000..999999}.tar
noise_fullband_shards: !ref <input_shards_dir>/noise_fullband/shard-{000000..999999}.tar

# Configuration for synthesizing shards of clean-noisy pairs.
samples_per_shard: 5000

# Destination directory for storing shards of synthesized data.
synthesized_data_dir: !PLACEHOLDER  #  synthesized_data_shards
train_shard_destination: !ref <synthesized_data_dir>/train_shards/<split_name>
valid_shard_destination: !ref <synthesized_data_dir>/valid_shards/<split_name>

# Set to a directory on a large disk if using Webdataset shards hosted on the web.
shard_cache_dir:

# These can be skipped. (uncomment if you want to use them)
# clean_singing: !PLACEHOLDER # ../DNS-shards/clean_fullband/VocalSet_48kHz_mono/
# clean_emotion: !PLACEHOLDER # ../DNS-shards/clean_fullband/emotional_speech/
## Aishell data needs to be downloaded separately.
# clean_mandarin: !PLACEHOLDER # ../DNS-shards/clean_fullband/mandrin_speech/data_aishell

log_dir: !ref <split_name>_logs
noise_types_excluded: None

## Config: add singing voice to clean speech
use_singing_data: 0 # 0 for no, 1 for yes
# 1 for only male, 2 for only female, 3 (default) for both male and female
singing_choice: 3

## Config: add emotional data to clean speech
# 0 for no, 1 for yes
use_emotion_data: 0

## Config: add Chinese (mandarin) data to clean speech
# 0 for no, 1 for yes
use_mandarin_data: 0

## Config: add reverb to clean speech
# 1 for only real rir, 2 for only synthetic rir, 3 (default) use both real and synthetic
rir_choice: 3
# lower bound of t60 range in seconds
lower_t60: 0.3
# upper bound of t60 range in seconds
upper_t60: 1.3
