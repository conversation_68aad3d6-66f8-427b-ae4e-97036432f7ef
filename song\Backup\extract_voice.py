import gradio as gr
import torchaudio
import torch
from speechbrain.inference import Speaker<PERSON><PERSON><PERSON><PERSON><PERSON>, VAD
import os
import ffmpeg
import glob
import tempfile
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import logging
from pathlib import Path
import json
from datetime import datetime

# -------------------------------------------------------------------
# Script de reconnaissance et extraction de segments de locuteur
# Adapté pour SpeechBrain 1.0.3+
# - Extraction audio vidéo en WAV PCM 16kHz mono
# - VAD via get_speech_prob_file
# - Gestion de dossiers, voix à exclure, seuils et sauvegarde des segments
# - Améliorations : logging, cache, parallélisation, statistiques
# -------------------------------------------------------------------

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('voice_extraction.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Répertoire courant
current_dir = os.getcwd()

# Cache pour les embeddings (évite de recalculer)
embedding_cache = {}

# Chargement des modèles pré-entraînés
verification = SpeakerRecognition.from_hparams(
    source="speechbrain/spkrec-ecapa-voxceleb",
    savedir=os.path.join(current_dir, "pretrained_models", "spkrec-ecapa-voxceleb")
)
ad_model = VAD.from_hparams(
    source="speechbrain/vad-crdnn-libriparty",
    savedir=os.path.join(current_dir, "pretrained_models", "vad-crdnn-libriparty")
)


def get_cached_embedding(audio_path, model):
    """Cache les embeddings pour éviter les recalculs."""
    if audio_path in embedding_cache:
        return embedding_cache[audio_path]

    sig, fs = torchaudio.load(audio_path)
    if sig.dim() > 1:
        sig = sig.mean(dim=0, keepdim=True)
    if fs != 16000:
        sig = torchaudio.transforms.Resample(fs, 16000)(sig)

    embedding = model.encode_batch(sig).squeeze()
    embedding_cache[audio_path] = embedding
    return embedding


def save_extraction_stats(output_dir, stats):
    """Sauvegarde les statistiques d'extraction."""
    stats_file = os.path.join(output_dir, f"extraction_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    logger.info(f"Statistiques sauvegardées : {stats_file}")


def extract_audio(input_path, output_wav):
    """
    Extrait la piste audio d'une vidéo (MP4/MKV) en WAV PCM 16kHz mono.
    Amélioré avec gestion multi-pistes et logging.
    """
    try:
        probe = ffmpeg.probe(input_path)
        streams = [s for s in probe["streams"] if s["codec_type"] == "audio"]
        if not streams:
            logger.warning(f"Aucune piste audio trouvée dans {input_path}")
            return False

        # Sélectionner la meilleure piste audio (français si disponible)
        best_stream = streams[0]
        for stream in streams:
            if 'tags' in stream and 'language' in stream['tags']:
                if stream['tags']['language'].lower() in ['fr', 'fre', 'fra']:
                    best_stream = stream
                    break

        idx = best_stream["index"]
        logger.info(f"Extraction audio piste {idx} de {input_path}")

        (
            ffmpeg
            .input(input_path)
            .output(
                output_wav,
                map=f"0:{idx}",
                vn=None,
                acodec="pcm_s16le",
                ar="16000",
                ac="1"
            )
            .overwrite_output()
            .run(quiet=True)
        )
        return True
    except ffmpeg.Error as e:
        logger.error(f"Erreur extraction audio pour {input_path} : {e.stderr}")
        return False


def extract_voice(
    target_audio_path,
    negative_audio_dir,
    input_path,
    output_dir,
    verification_threshold=0.7,
    negative_threshold=0.3,
    min_segment_duration=0.5,
    vad_threshold=0.5
):
    # Charger embeddings négatifs
    negative_embeddings = []
    if negative_audio_dir and os.path.isdir(negative_audio_dir):
        for ext in ("*.wav","*.mp3","*.flac","*.ogg"):  
            for neg_path in glob.glob(os.path.join(negative_audio_dir, ext)):
                sig_neg, fs_neg = torchaudio.load(neg_path)
                if sig_neg.dim()>1:
                    sig_neg = sig_neg.mean(dim=0, keepdim=True)
                if fs_neg != 16000:
                    sig_neg = torchaudio.transforms.Resample(fs_neg,16000)(sig_neg)
                negative_embeddings.append(verification.encode_batch(sig_neg).squeeze())
        print(f"{len(negative_embeddings)} embeddings négatifs chargés.")
    else:
        print("Pas de dossier d'exclusion valide. Continu sans exclusions.")

    # Charger et encoder locuteur cible
    sig_t, fs_t = torchaudio.load(target_audio_path)
    if sig_t.dim()>1:
        sig_t = sig_t.mean(dim=0, keepdim=True)
    if fs_t != 16000:
        sig_t = torchaudio.transforms.Resample(fs_t,16000)(sig_t)
    target_emb = verification.encode_batch(sig_t).squeeze()
    print("Empreinte cible calculée.")

    # Préparer liste de fichiers à traiter
    if os.path.isdir(input_path):
        exts = ("*.wav","*.mp3","*.flac","*.ogg","*.mp4","*.mkv")
        to_process = []
        for e in exts:
            to_process += glob.glob(os.path.join(input_path, e))
        if not to_process:
            return f"Aucun fichier audio/vidéo trouvé dans {input_path}"
        print(f"{len(to_process)} fichiers détectés pour traitement.")
    elif os.path.isfile(input_path):
        to_process = [input_path]
    else:
        return f"Chemin invalide : {input_path}"

    os.makedirs(output_dir, exist_ok=True)
    saved_segments = []

    # Boucle de traitement
    for path in to_process:
        print(f"--- Traitement : {path} ---")
        try:
            # Extraction audio si nécessaire
            ext = os.path.splitext(path)[1].lower()
            if ext in ('.mp4','.mkv'):
                tmp_wav = tempfile.NamedTemporaryFile(suffix='.wav', delete=False).name
                if not extract_audio(path, tmp_wav):
                    continue
                sig, fs = torchaudio.load(tmp_wav)
                os.remove(tmp_wav)
            else:
                sig, fs = torchaudio.load(path)

            # Conversion en mono
            if sig.dim()>1:
                sig = sig.mean(dim=0, keepdim=True)
            sig_orig = sig.clone()

            # Preparer VAD
            if fs != 16000:
                sig_vad = torchaudio.transforms.Resample(fs,16000)(sig)
                fs_vad = 16000
            else:
                sig_vad, fs_vad = sig, fs

            # VAD via fichier temporaire
            tmp_v = tempfile.NamedTemporaryFile(suffix='.wav', delete=False).name
            torchaudio.save(tmp_v, sig_vad, fs_vad)
            speech_probs = ad_model.get_speech_prob_file(audio_file=tmp_v).squeeze()
            os.remove(tmp_v)
            flags = (speech_probs > vad_threshold).int().tolist()

            # Identifier segments
            segments = []
            start = None
            for i, f in enumerate(flags):
                if f and start is None:
                    start = i
                if not f and start is not None:
                    end = i
                    dur = (end - start) * 0.01
                    if dur >= min_segment_duration:
                        segments.append((start, end))
                    start = None
            if start is not None:
                segments.append((start, len(flags)))

            # Fusion silences courts (<=0.5s)
            merged = []
            for s,e in segments:
                if merged and (s - merged[-1][1]) <= int(0.5/0.01):
                    merged[-1] = (merged[-1][0], e)
                else:
                    merged.append((s,e))

            # Extraction, embeddings et sauvegarde
            for idx, (s,e) in enumerate(merged, 1):
                start_samp = int(s * 0.01 * fs)
                end_samp   = int(e * 0.01 * fs)
                seg = sig_orig[:, start_samp:end_samp]
                # Embedding segment
                seg_v = seg if fs==16000 else torchaudio.transforms.Resample(fs,16000)(seg)
                if seg_v.dim()>1:
                    seg_v = seg_v.mean(dim=0, keepdim=True)
                with torch.no_grad():
                    emb = verification.encode_batch(seg_v).squeeze()
                sim = torch.cosine_similarity(emb, target_emb, dim=0).item()
                if sim < verification_threshold:
                    continue
                # Exclusion
                skip = False
                for neg_emb in negative_embeddings:
                    if torch.cosine_similarity(emb, neg_emb, dim=0).item() >= negative_threshold:
                        skip = True
                        break
                if skip:
                    continue
                # Sauvegarde segment
                base = os.path.splitext(os.path.basename(path))[0]
                out_wav = os.path.join(output_dir, f"{base}_seg_{idx}.wav")
                torchaudio.save(out_wav, seg, fs)
                saved_segments.append(out_wav)
                print(f"Segment {idx} sauvegardé → {out_wav}")

        except Exception as e:
            print(f"Erreur sur {path} : {e}")
            continue

    return f"Segments sauvés : {len(saved_segments)}\n" + "\n".join(saved_segments)

# Interface Gradio
iface = gr.Interface(
    fn=extract_voice,
    inputs=[
        gr.Textbox(label="Chemin audio cible (locuteur)", value="H:\IA\speechbrain\song"),
        gr.Textbox(label="Dossier voix à exclure (facultatif)"), value="H:\IA\speechbrain\song\Exclure"),
        gr.Textbox(label="Dossier/fichier à analyser"), value="H:\IA\speechbrain\song\input"),
        gr.Textbox(label="Répertoire de sortie"), value="H:\IA\speechbrain\song\output"),
        gr.Slider(0.0,1.0,0.01,0.7,label="Seuil similarité cible"),
        gr.Slider(0.0,1.0,0.01,0.3,label="Seuil similarité exclue"),
        gr.Slider(0.0,5.0,0.1,0.5,label="Durée min segment (s)"),
        gr.Slider(0.0,1.0,0.01,0.5,label="Seuil VAD")
    ],
    outputs="text",
    title="Détection de voix PEPE 1.0.3+",
)
iface.launch(inbrowser=True)
