# wav2vec 2.0 pretraining with SpeechBrain and HuggingFace <img src="https://huggingface.co/front/assets/huggingface_logo.svg" alt="drawing" width="40"/>
This folder contains the scripts to train a wav2vec2 based system using CommonVoice. It can be adapted to any dataset as long as you provide the csv or json files as with other recipes. No other adaptation will be required apart from controlling the sequence length to avoid out of memory issues.

# SpeechBrain VS HuggingFace wav2vec2 training ??
As usual, our goal at SpeechBrain remains to offer as much flexibility to the user as possible. Hence, wav2vec2 pretraining can be achieved in two different ways: fully with SpeechBrain, or following our HuggingFace interface. Both approaches give similar results. Indeed we tested both with a BASE model pretrained on LibriSpeech and fine-tuned on LibriSpeech for ASR, IEMOCAP for emotion recognition and VoxCeleb 1 for speaker identification. Therefore, it is up to the user to decide what training scheme he/she wish to follow. A full SpeechBrain training offers a unique flexibility for further research (e.g. changing the loss, changing the architecture, modifying absolutely everything with wav2vec2), while the HuggingFace pretraining offers a good interfacing with the transformers library.

**On CommonVoice, we officially provide only a fully HuggingFace recipe. If you wish to use the HuggingFace pretraining, please go to our [LibriSpeech recipe](https://github.com/speechbrain/speechbrain/tree/develop/recipes/LibriSpeech/self-supervised-learning/wav2vec2)**

# Principle
The idea is extremely simple. <img src="https://huggingface.co/front/assets/huggingface_logo.svg" alt="drawing" width="40"/> provides a wav2vec 2.0 loss calculation. In practice, it means that forwarding throughout their wav2vec 2.0 models returns the loss. Hence, we simply use this interface as a lobes wrapper in SpeechBrain so anyone can fully pretrain a wav2vec 2.0 model.

At a high level, the steps of this integration are:
1. Indicate a <img src="https://huggingface.co/front/assets/huggingface_logo.svg" alt="drawing" width="40"/> repository that stores the wav2vec 2.0 config file. This is necessary to determine the architecture of the model that will be instantiated (see `wav2vec2_hub` in the yaml). You can browse all the existing HuggingFace architectures online and use them! In practice, SpeechBrain will download the configuration file corresponding (or load it locally), and instantiate in PyTorch the wav2vec 2.0 model.
2. Train it using our wrapper and this recipe.
3. Save it to be reused as a finetunable or frozen encoder with SpeechBrain recipes (as we already have for several task).

# Go !
Simply type:
`python train.py hparams/wav2vec2_base.yaml --find_unused_parameters`

Do not forget to replace the `!PLACEHOLDER` variables in the yaml corresponding to your local path to the data.

# Use a pretrained model for fine-tuning with SpeechBrain

The checkpoint generated by this pretraining is a standard PyTorch checkpoint. If you wish to use it as any pretrained HuggingFace model, as you would do for all the recipes that we currently have for wav2vec 2.0 finetuning, you simply need to copy this checkpoint to a folder that contains the corresponding `config.json` and `preprocessor_config.json`. Indeed, SpeechBrain depends (for now) from HuggingFace to train the wav2vec 2.0 model, and these files are the way HuggingFace defines all the parameters of the model. They usually can be found directly on the HuggingFace repository. Then, you just have to use the `speechbrain.lobes.models.huggingface_transformers.wav2vec2.Wav2Vec2` (e.g., [CommonVoice FR ASR](https://github.com/speechbrain/speechbrain/blob/develop/recipes/CommonVoice/ASR/CTC/hparams/train_fr_with_wav2vec.yaml)) class and give the `wav2vec2_hub:/my/path/to/my/speechbrain_wav2vec2_model` parameter, and your pretrained model will be loaded directly for downstream training!

# Advices
Training wav2vec 2.0 models is crazy w.r.t compute resources. For instance, this recipe only trains a BASE wav2vec 2.0 architecture, and it already requires 16 Tesla V100 for 7 days. Of course, you can scale this to your needs (e.g., you can work with 2 GPUs only), but it will take ages! Welcome to the wav2vec 2.0 world!

Here is a list of the most important advices:
- To train w2v2 models, it is **extremely** important to have an effective batch size as high as possible. For instance, the original BASE model is trained with batches containing 1.6H of speech. This means that (duration_per_minibatch * nb_gpu * grad_accumulation_factor) must be at least equal to 1.6H.
- Do not train on sequences longer than 20s, this will blow your VRAM up and is useless for now. Indeed training with shorter sentences (10s) may work just as well.
- Set the `n_warmup_steps` steps in such a way that it corresponds to 10% of the total training steps. The number of steps correspond to the actual number of call to .backward w.r.t the batch size.

# Results and comparison with Fairseq
We compared our model to one trained with Fairseq on the exact same condition. Our model obtained better performance than the Fairseq implementation on three downstream tasks: speech recognition, emotion recognition and speaker verification. No worries, it works well. Our results will be similar to the ones obtained with the HuggingFace implementation, as they are equivalent.
