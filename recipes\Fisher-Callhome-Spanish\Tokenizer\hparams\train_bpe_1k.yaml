# ############################################################################
# Tokenizer: subword BPE tokenizer with BPE 1K
# Training: Fisher-Callhome 160h
# Authors: <AUTHORS>
# ############################################################################


# Set up folders for reading from and writing to
original_data_folder: !PLACEHOLDER # i.e., path to the original data contain LDCXXX
data_folder: !PLACEHOLDER # Path where to store the .json and prepared data
output_folder: !PLACEHOLDER # Path where to store theTokenizer output (model, logs etc)
device: "cuda:0" # for resample audio
skip_prep: False
train_annotation: !ref <data_folder>/train/data.json

# Tokenizer parameters
token_type: bpe  # ["unigram", "bpe", "char"]
token_output: 1000
# transcription: transcription in source language
# translation_0: translation in target language
# transcription_and_translation: joint transcription and translation
annotation_read: "transcription_and_translation" # field to read

# Tokenizer object
tokenizer: !name:speechbrain.tokenizers.SentencePiece.SentencePiece
   model_dir: !ref <output_folder>
   vocab_size: !ref <token_output>
   annotation_train: !ref <train_annotation>
   annotation_read: !ref <annotation_read>
   model_type: !ref <token_type> # ["unigram", "bpe", "char"]
   annotation_list_to_check: [!ref <train_annotation>]
   annotation_format: json
   bos_id: 1
   eos_id: 2
   unk_id: 0
