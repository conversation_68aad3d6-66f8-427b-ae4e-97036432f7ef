"""
Configuration avancée pour l'extracteur de voix
Permet de personnaliser finement les paramètres d'extraction
"""

import json
import os
from dataclasses import dataclass, asdict
from typing import List, Optional

@dataclass
class VADConfig:
    """Configuration pour la détection d'activité vocale"""
    use_hystheresis: bool = True
    activation_threshold: float = 0.6
    deactivation_threshold: float = 0.4
    simple_threshold: float = 0.5
    min_speech_duration: float = 0.1  # Durée minimale pour considérer comme parole
    max_silence_duration: float = 0.5  # Durée max de silence pour fusionner

@dataclass
class SimilarityConfig:
    """Configuration pour la similarité des locuteurs"""
    verification_threshold: float = 0.7
    negative_threshold: float = 0.3
    use_adaptive_threshold: bool = False  # Ajustement automatique selon la qualité
    quality_boost_factor: float = 0.1  # Bonus pour les segments de haute qualité

@dataclass
class AudioConfig:
    """Configuration pour le traitement audio"""
    target_sample_rate: int = 16000
    preferred_languages: List[str] = None  # ['fr', 'fre', 'fra']
    audio_quality_threshold: float = 0.8  # Seuil de qualité audio
    normalize_audio: bool = True
    apply_noise_reduction: bool = False

@dataclass
class OutputConfig:
    """Configuration pour la sortie"""
    filename_template: str = "{base}_seg{idx:03d}_{timestamp}_sim{similarity:.3f}.wav"
    save_metadata: bool = True
    create_playlist: bool = True  # Créer une playlist M3U
    max_segments_per_file: int = 1000  # Limite pour éviter trop de fichiers

@dataclass
class ProcessingConfig:
    """Configuration pour le traitement"""
    max_workers: int = 2  # Nombre de threads pour le traitement parallèle
    chunk_size: int = 10  # Taille des chunks pour le traitement par batch
    enable_caching: bool = True
    cache_embeddings: bool = True
    log_level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR

@dataclass
class AdvancedConfig:
    """Configuration complète avancée"""
    vad: VADConfig
    similarity: SimilarityConfig
    audio: AudioConfig
    output: OutputConfig
    processing: ProcessingConfig
    
    def __post_init__(self):
        if self.audio.preferred_languages is None:
            self.audio.preferred_languages = ['fr', 'fre', 'fra']

def load_config(config_path: str = "config_advanced.json") -> AdvancedConfig:
    """Charge la configuration depuis un fichier JSON"""
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return AdvancedConfig(
            vad=VADConfig(**data.get('vad', {})),
            similarity=SimilarityConfig(**data.get('similarity', {})),
            audio=AudioConfig(**data.get('audio', {})),
            output=OutputConfig(**data.get('output', {})),
            processing=ProcessingConfig(**data.get('processing', {}))
        )
    else:
        # Configuration par défaut
        return get_default_config()

def save_config(config: AdvancedConfig, config_path: str = "config_advanced.json"):
    """Sauvegarde la configuration dans un fichier JSON"""
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(asdict(config), f, indent=2, ensure_ascii=False)

def get_default_config() -> AdvancedConfig:
    """Retourne la configuration par défaut"""
    return AdvancedConfig(
        vad=VADConfig(),
        similarity=SimilarityConfig(),
        audio=AudioConfig(),
        output=OutputConfig(),
        processing=ProcessingConfig()
    )

def get_preset_configs() -> dict:
    """Retourne des configurations prédéfinies pour différents cas d'usage"""
    
    # Configuration haute précision (moins de segments mais plus fiables)
    high_precision = get_default_config()
    high_precision.similarity.verification_threshold = 0.8
    high_precision.similarity.negative_threshold = 0.2
    high_precision.vad.activation_threshold = 0.7
    high_precision.vad.deactivation_threshold = 0.3
    
    # Configuration haute sensibilité (plus de segments, moins strict)
    high_sensitivity = get_default_config()
    high_sensitivity.similarity.verification_threshold = 0.6
    high_sensitivity.similarity.negative_threshold = 0.4
    high_sensitivity.vad.activation_threshold = 0.5
    high_sensitivity.vad.deactivation_threshold = 0.5
    
    # Configuration pour audio de mauvaise qualité
    low_quality_audio = get_default_config()
    low_quality_audio.similarity.verification_threshold = 0.65
    low_quality_audio.vad.activation_threshold = 0.55
    low_quality_audio.vad.max_silence_duration = 0.8
    low_quality_audio.audio.apply_noise_reduction = True
    
    # Configuration pour traitement rapide
    fast_processing = get_default_config()
    fast_processing.processing.max_workers = 4
    fast_processing.processing.chunk_size = 20
    fast_processing.vad.use_hystheresis = False
    
    return {
        "default": get_default_config(),
        "high_precision": high_precision,
        "high_sensitivity": high_sensitivity,
        "low_quality_audio": low_quality_audio,
        "fast_processing": fast_processing
    }

def create_config_file_with_presets():
    """Crée un fichier de configuration avec tous les presets"""
    presets = get_preset_configs()
    
    config_data = {
        "current_preset": "default",
        "presets": {name: asdict(config) for name, config in presets.items()},
        "custom": asdict(get_default_config())
    }
    
    with open("config_presets.json", 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    print("✅ Fichier config_presets.json créé avec tous les presets disponibles")

def load_preset(preset_name: str) -> AdvancedConfig:
    """Charge un preset spécifique"""
    presets = get_preset_configs()
    if preset_name in presets:
        return presets[preset_name]
    else:
        print(f"⚠️ Preset '{preset_name}' introuvable. Presets disponibles: {list(presets.keys())}")
        return get_default_config()

# Fonctions utilitaires pour l'interface
def get_config_summary(config: AdvancedConfig) -> str:
    """Retourne un résumé lisible de la configuration"""
    return f"""
📋 **Résumé de la configuration:**

🔊 **VAD:**
- Hystérésis: {'✅' if config.vad.use_hystheresis else '❌'}
- Seuils: {config.vad.activation_threshold:.2f} / {config.vad.deactivation_threshold:.2f}
- Durée min parole: {config.vad.min_speech_duration}s

🎯 **Similarité:**
- Seuil cible: {config.similarity.verification_threshold:.2f}
- Seuil exclusion: {config.similarity.negative_threshold:.2f}
- Adaptation auto: {'✅' if config.similarity.use_adaptive_threshold else '❌'}

🎵 **Audio:**
- Fréquence: {config.audio.target_sample_rate} Hz
- Langues préférées: {', '.join(config.audio.preferred_languages)}
- Normalisation: {'✅' if config.audio.normalize_audio else '❌'}

💾 **Sortie:**
- Template: {config.output.filename_template}
- Métadonnées: {'✅' if config.output.save_metadata else '❌'}
- Playlist: {'✅' if config.output.create_playlist else '❌'}

⚙️ **Traitement:**
- Workers: {config.processing.max_workers}
- Cache: {'✅' if config.processing.enable_caching else '❌'}
- Log level: {config.processing.log_level}
"""

if __name__ == "__main__":
    # Créer les fichiers de configuration par défaut
    print("🔧 Création des fichiers de configuration...")
    
    # Configuration par défaut
    default_config = get_default_config()
    save_config(default_config, "config_advanced.json")
    print("✅ config_advanced.json créé")
    
    # Fichier avec tous les presets
    create_config_file_with_presets()
    
    # Afficher le résumé de la config par défaut
    print(get_config_summary(default_config))
    
    print("\n🎯 **Utilisation:**")
    print("1. Modifiez config_advanced.json pour personnaliser")
    print("2. Ou utilisez un preset depuis config_presets.json")
    print("3. Importez dans votre script: from config_advanced import load_config")
