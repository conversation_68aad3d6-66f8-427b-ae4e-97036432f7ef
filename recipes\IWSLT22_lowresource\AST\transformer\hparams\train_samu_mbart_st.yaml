# ############################################################################
# Model: E2E ST with SAMU encoder and mBART decoder
# Encoder: SAMU
# Decoder: mBART decoder
# losses: NLL
# Training: Tamasheq-French corpus
# Author:  Ha Nguyen, 2023
# ############################################################################

# Seed needs to be set at top of yaml, before objects with parameters are made
seed: 1337 #7777
__set_seed: !!python/object/apply:torch.manual_seed [!ref <seed>]
debug: False
output_folder: !ref results/samu_mbart/<seed>
save_folder: !ref <output_folder>/save
train_log: !ref <output_folder>/train_log.txt
wer_file: !ref <output_folder>/wer.txt
bleu_file: !ref <output_folder>/bleu.txt

# root data folder points to 17h version inside the github folder (IWSLT2022_Tamasheq_data/taq_fra_clean/)
root_data_folder: !PLACEHOLDER # e.g., /users/hnguyen/IWSLT2022_Tamasheq_data/taq_fra_clean
# data folder is the place where the json files will be stored prior to training
data_folder: !ref <root_data_folder>/json_version/
lang: "fr" #for the BLEU score detokenization
target_lang: "fr_XX" # for mbart initialization

annotation_train: !ref <data_folder>/train.json
annotation_valid: !ref <data_folder>/valid.json
annotation_test: !ref <data_folder>/test.json
skip_prep: False

# URL for the HuggingFace model we want to load (BASE here)
wav2vec2_hub: LIA-AvignonUniversity/IWSLT2022-tamasheq-only
wav2vec2_folder: !ref <save_folder>/wav2vec2_checkpoint

# wav2vec 2.0 specific parameters
wav2vec2_frozen: False

####################### Training Parameters ####################################
number_of_epochs: 500
lr: 0.001
lr_wav2vec: 0.0001
lr_mbart: 0.0001
batch_size: 2
test_batch_size: 1
grad_accumulation_factor: 6
valid_search_interval: 4
loss_reduction: batchmean
ckpt_interval_minutes: 15 # save checkpoint every N min

# Data sorting parameters: sorting_debug_duration replaces sorting_min_duration in debug mode
sorting: ascending

epoch_counter: !new:speechbrain.utils.epoch_loop.EpochCounter
    limit: !ref <number_of_epochs>

dataloader_options:
    batch_size: !ref <batch_size>
    num_workers: 4

test_dataloader_options:
    batch_size: !ref <test_batch_size>
    num_workers: 4

# Feature parameters (W2V2 etc)
features_dim: 768 # base wav2vec output dimension, for large replace by 1024

#projection for w2v
enc_dnn_layers: 1
enc_dnn_neurons: 1024 #256

# Transformer
activation: !name:torch.nn.GELU

# Outputs
label_smoothing: 0.1
pad_index: 1      # pad_index defined by mbart model
bos_index: 250008 # fr_XX bos_index defined by mbart model
eos_index: 2

# Decoding parameters
# Be sure that the bos and eos index match with the BPEs ones
min_decode_ratio: 0.0
max_decode_ratio: 0.25
valid_beam_size: 5

############################## models ################################
#wav2vec model
wav2vec2: !new:speechbrain.lobes.models.huggingface_transformers.wav2vec2.Wav2Vec2
    source: !ref <wav2vec2_hub>
    output_norm: True
    freeze: !ref <wav2vec2_frozen>
    save_path: !ref <wav2vec2_folder>

#linear projection
enc: !new:speechbrain.lobes.models.VanillaNN.VanillaNN
    input_shape: [null, null, !ref <features_dim>]
    activation: !ref <activation>
    dnn_blocks: !ref <enc_dnn_layers>
    dnn_neurons: !ref <enc_dnn_neurons>

#mBART
mbart_path: facebook/mbart-large-50-many-to-many-mmt
mbart_frozen: False
vocab_size: 250054
mBART: !new:speechbrain.lobes.models.huggingface_transformers.mbart.mBART
    source: !ref <mbart_path>
    freeze: !ref <mbart_frozen>
    save_path: !ref <save_folder>/mbart_checkpoint
    target_lang: !ref <target_lang>

log_softmax: !new:speechbrain.nnet.activations.Softmax
    apply_log: True

modules:
    wav2vec2: !ref <wav2vec2>
    enc: !ref <enc>
    mBART: !ref <mBART>

model: !new:torch.nn.ModuleList
    - [!ref <enc>]

adam_opt_class: !name:torch.optim.Adam
    lr: !ref <lr>

wav2vec_opt_class: !name:torch.optim.Adam
    lr: !ref <lr_wav2vec>

mbart_opt_class: !name:torch.optim.Adam
    lr: !ref <lr_mbart>

seq_cost: !name:speechbrain.nnet.losses.nll_loss
    label_smoothing: !ref <label_smoothing>
    reduction: !ref <loss_reduction>

lr_annealing_adam: !new:speechbrain.nnet.schedulers.NewBobScheduler
    initial_value: !ref <lr>
    improvement_threshold: 0.0025
    annealing_factor: 0.5
    patient: 2

warmup: 8000
hold: 32000
cooldown: 40000
optimizer_step_limit: 80000

lr_annealing_wav2vec: !new:speechbrain.nnet.schedulers.TriStageLRSchedule
    lr: !ref <lr_wav2vec>
    warmup_steps: !ref <warmup>
    hold_steps: !ref <hold>
    decay_steps: !ref <cooldown>
    total_steps: !ref <optimizer_step_limit>

lr_annealing_mbart: !new:speechbrain.nnet.schedulers.TriStageLRSchedule
    lr: !ref <lr_mbart>
    warmup_steps: !ref <warmup>
    hold_steps: !ref <hold>
    decay_steps: !ref <cooldown>
    total_steps: !ref <optimizer_step_limit>

checkpointer: !new:speechbrain.utils.checkpoints.Checkpointer
    checkpoints_dir: !ref <save_folder>
    recoverables:
        model: !ref <model>
        wav2vec2: !ref <wav2vec2>
        mBART: !ref <mBART>
        lr_annealing_wav2vec: !ref <lr_annealing_wav2vec>
        lr_annealing_mbart: !ref <lr_annealing_mbart>
        counter: !ref <epoch_counter>

valid_search: !new:speechbrain.decoders.S2SHFTextBasedBeamSearcher
    modules: [!ref <mBART>, null, null]
    vocab_size: !ref <vocab_size>
    bos_index: !ref <bos_index>
    eos_index: !ref <eos_index>
    min_decode_ratio: !ref <min_decode_ratio>
    max_decode_ratio: !ref <max_decode_ratio>
    beam_size: !ref <valid_beam_size>
    using_eos_threshold: True
    length_normalization: True

train_logger: !new:speechbrain.utils.train_logger.FileTrainLogger
    save_file: !ref <train_log>

bleu_computer: !name:speechbrain.utils.bleu.BLEUStats
    merge_words: False

acc_computer: !name:speechbrain.utils.Accuracy.AccuracyStats

# Path to the samu checkpoint
pre_trained_samu: !PLACEHOLDER # e.g., /users/hnguyen/output_samu_pretraining/7777/save/CKPT+checkpoint_epoch100/wav2vec2.ckpt
pretrainer: !new:speechbrain.utils.parameter_transfer.Pretrainer
    collect_in: !ref <save_folder>
    loadables:
        wav2vec: !ref <wav2vec2>
    paths:
        wav2vec: !ref <pre_trained_samu>
