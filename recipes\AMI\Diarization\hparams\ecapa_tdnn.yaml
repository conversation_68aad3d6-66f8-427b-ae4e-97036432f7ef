# ##################################################
# Model: Speaker Diarization Baseline
# Embeddings: Deep embedding
# Clustering Technique: Spectral clustering
# Authors: <AUTHORS>
# #################################################

seed: 1234
__set_seed: !apply:torch.manual_seed [!ref <seed>]

# Directories: Replace !PLACEHOLDER with full path of the directory.
# Download data from: http://groups.inf.ed.ac.uk/ami/download/
data_folder: !PLACEHOLDER # e.g., /path/to/amicorpus/

# Download manual annotations from: http://groups.inf.ed.ac.uk/ami/download/
manual_annot_folder: !PLACEHOLDER # e.g., /path/to/ami_public_manual_1.6.2/

output_folder: results/ami/ecapa/
save_folder: !ref <output_folder>/save
skip_prep: False

# Embedding model
# Here, the pretrained embedding model trained with train_speaker_embeddings.py hparams/train_ecapa_tdnn.yaml
# is downloaded from the speechbrain HuggingFace repository.
# However, a local path pointing to a directory containing your checkpoints may also be specified
# instead (see pretrainer below)

# Will automatically download ECAPA-TDNN model (best).
pretrain_path: speechbrain/spkrec-ecapa-voxceleb

# Some more exp folders (for cleaner structure).
embedding_dir: !ref <save_folder>/emb
meta_data_dir: !ref <save_folder>/metadata
ref_rttm_dir: !ref <save_folder>/ref_rttms
sys_rttm_dir: !ref <save_folder>/sys_rttms
der_dir: !ref <save_folder>/DER

# Spectral feature parameters
n_mels: 80
# left_frames: 0
# right_frames: 0
# deltas: False

# ECAPA-TDNN model
emb_dim: 192
emb_channels: [1024, 1024, 1024, 1024, 3072]
emb_attention_channels: 128
emb_lin_neurons: 192
batch_size: 512

# AMI data_prep parameters
split_type: 'full_corpus_asr'
skip_TNO: True
# Options for mic_type: 'Mix-Lapel', 'Mix-Headset', 'Array1', 'Array1-01', 'BeamformIt'
mic_type: 'Mix-Headset'
dev_meta_file: !ref <meta_data_dir>/ami_dev.<mic_type>.subsegs.json
eval_meta_file: !ref <meta_data_dir>/ami_eval.<mic_type>.subsegs.json
vad_type: 'oracle'
max_subseg_dur: 3.0
overlap: 1.5

backend: 'SC' # options: 'kmeans' # Note: kmeans goes only with cos affinity

# Spectral Clustering parameters
affinity: 'cos'  # options: cos, nn
max_num_spkrs: 10
oracle_n_spkrs: True

# DER evaluation parameters
ignore_overlap: True
forgiveness_collar: 0.25

# Used for multi-mic beamformer
sampling_rate: 16000

dataloader_opts:
    batch_size: !ref <batch_size>

compute_features: !new:speechbrain.lobes.features.Fbank
    n_mels: !ref <n_mels>

multimic_beamformer: !new:speechbrain.lobes.beamform_multimic.DelaySum_Beamformer
    sampling_rate: !ref <sampling_rate>

mean_var_norm: !new:speechbrain.processing.features.InputNormalization
    norm_type: sentence
    std_norm: False

embedding_model: !new:speechbrain.lobes.models.ECAPA_TDNN.ECAPA_TDNN
    input_size: !ref <n_mels>
    channels: !ref <emb_channels>
    kernel_sizes: [5, 3, 3, 3, 1]
    dilations: [1, 2, 3, 4, 1]
    attention_channels: !ref <emb_attention_channels>
    lin_neurons: !ref <emb_lin_neurons>

mean_var_norm_emb: !new:speechbrain.processing.features.InputNormalization
    norm_type: global
    std_norm: False

pretrainer: !new:speechbrain.utils.parameter_transfer.Pretrainer
    collect_in: !ref <save_folder>
    loadables:
        embedding_model: !ref <embedding_model>
    paths:
        embedding_model: !ref <pretrain_path>/embedding_model.ckpt
