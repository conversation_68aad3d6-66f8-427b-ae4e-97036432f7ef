# ##################################################
# Model: Speaker Diarization Baseline
# Embeddings: Deep embedding
# Clustering Technique: Spectral clustering
# Authors: <AUTHORS>
# #################################################

seed: 1234
__set_seed: !apply:torch.manual_seed [!ref <seed>]

# Folders
# Download data from: http://groups.inf.ed.ac.uk/ami/download/
data_folder: !PLACEHOLDER # e.g., /path/to/amicorpus/

# Download manual annotations from: http://groups.inf.ed.ac.uk/ami/download/
manual_annot_folder: !PLACEHOLDER # e.g., /path/to/ami_public_manual_1.6.2/

output_folder: results/ami/xvect/
save_folder: !ref <output_folder>/save/
skip_prep: False

# Embedding model
# Here, the pretrained embedding model trained with train_speaker_embeddings.py hparams/train_ecapa_tdnn.yaml
# is downloaded from the speechbrain HuggingFace repository.
# However, a local path pointing to a directory containing your checkpoints may also be specified
# instead (see pretrainer below)
pretrain_path: speechbrain/spkrec-xvect-voxceleb


# Some more exp folders (for cleaner structure)
embedding_dir: !ref <save_folder>/emb
meta_data_dir: !ref <save_folder>/metadata
ref_rttm_dir: !ref <save_folder>/ref_rttms
sys_rttm_dir: !ref <save_folder>/sys_rttms
der_dir: !ref <save_folder>/DER

# Spectral feature parameters
n_mels: 24
# left_frames: 0
# right_frames: 0
# deltas: False

# Xvector model
emb_dim: 512
emb_tdnn_channels: [512, 512, 512, 512, 1500]
batch_size: 512

# AMI data_prep parameters
split_type: 'full_corpus_asr'
skip_TNO: True
# Options for mic_type: 'Mix-Lapel', 'Mix-Headset', 'Array1', 'Array1-01', 'BeamformIt'
mic_type: 'Mix-Headset'
dev_meta_file: !ref <meta_data_dir>/ami_dev.<mic_type>.subsegs.json
eval_meta_file: !ref <meta_data_dir>/ami_eval.<mic_type>.subsegs.json
vad_type: 'oracle'
max_subseg_dur: 3.0
overlap: 1.5

backend: 'SC'

# Spectral Clustering parameters
affinity: 'cos'
max_num_spkrs: 10
oracle_n_spkrs: True

# DER evaluation parameters
ignore_overlap: True
forgiveness_collar: 0.25

dataloader_opts:
    batch_size: !ref <batch_size>

# Model params
compute_features: !new:speechbrain.lobes.features.Fbank
    n_mels: !ref <n_mels>

mean_var_norm: !new:speechbrain.processing.features.InputNormalization
    norm_type: sentence
    std_norm: False

embedding_model: !new:speechbrain.lobes.models.Xvector.Xvector
    in_channels: !ref <n_mels>
    activation: !name:torch.nn.LeakyReLU
    tdnn_blocks: 5
    tdnn_channels: !ref <emb_tdnn_channels>
    tdnn_kernel_sizes: [5, 3, 3, 1, 1]
    tdnn_dilations: [1, 2, 3, 1, 1]
    lin_neurons: !ref <emb_dim>

mean_var_norm_emb: !new:speechbrain.processing.features.InputNormalization
    norm_type: global
    std_norm: False

# compute_plda: !new:speechbrain.processing.PLDA_LDA.PLDA
#    rank_f: 100
#    nb_iter: 10
#    scaling_factor: 0.05

pretrainer: !new:speechbrain.utils.parameter_transfer.Pretrainer
    collect_in: !ref <save_folder>
    loadables:
        embedding_model: !ref <embedding_model>
        mean_var_norm_emb: !ref <mean_var_norm_emb>
    paths:
        embedding_model: !ref <pretrain_path>/embedding_model.ckpt
        mean_var_norm_emb: !ref <pretrain_path>/mean_var_norm_emb.ckpt
