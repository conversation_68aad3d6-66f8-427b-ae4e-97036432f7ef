# ############################################################################
# Model: CTC-wav2vec2
# Encoder: wav2vec2
# Decoder: -
# Tokens: Char
# losses: CTC
# Training: AISHELL-1
# Authors: <AUTHORS>
# ############################################################################

seed: 2
__set_seed: !apply:torch.manual_seed [!ref <seed>]
output_folder: !ref results/ctc_wav2vec2/<seed>
cer_file: !ref <output_folder>/cer.txt
save_folder: !ref <output_folder>/save
train_log: !ref <output_folder>/train_log.txt

# Data files
data_folder: !PLACEHOLDER # e,g./path/to/aishell

skip_prep: False
remove_compressed_wavs: False
ckpt_interval_minutes: 15 # save checkpoint every N min
train_data: !ref <output_folder>/train.csv
valid_data: !ref <output_folder>/dev.csv
test_data: !ref <output_folder>/test.csv

wav2vec2_hub: TencentGameMate/chinese-wav2vec2-large
wav2vec2_folder: !ref <save_folder>/wav2vec2_checkpoint

####################### Training Parameters ####################################

number_of_epochs: 80
lr: 1.0
lr_wav2vec: 0.0001
sorting: ascending
precision: fp32 # bf16, fp16 or fp32
sample_rate: 16000

# With data_parallel batch_size is split into N jobs
# With DDP batch_size is multiplied by N jobs
# Must be 8 per GPU to fit 32GB of VRAM
batch_size: 10
test_batch_size: 1

dynamic_batching: False
max_batch_length: 15 # in terms of "duration" in annotations by default, second here
shuffle: False # if true re-creates batches at each epoch shuffling examples.
num_buckets: 10 # floor(log(max_batch_len/left_bucket_len, multiplier)) + 1
batch_ordering: ascending
dynamic_batch_sampler:
   max_batch_length: !ref <max_batch_length>
   shuffle: !ref <shuffle>
   num_buckets: !ref <num_buckets>
   batch_ordering: !ref <batch_ordering>

num_workers: 6

# Dataloader options
train_dataloader_opts:
   batch_size: !ref <batch_size>
   num_workers: !ref <num_workers>
valid_dataloader_opts:
   batch_size: !ref <test_batch_size>
   num_workers: !ref <num_workers>
test_dataloader_opts:
   batch_size: !ref <test_batch_size>
   num_workers: !ref <num_workers>

wav2vec_output_dim: 1024
dnn_neurons: 1024
freeze_wav2vec: False
dropout: 0.15

tokenizer: !apply:transformers.BertTokenizer.from_pretrained
   pretrained_model_name_or_path: bert-base-chinese
# bert-base-chinese tokens length
output_neurons: 21128

############################## Decoding ########################################

# Be sure that the bos and eos index match with the BPEs ones
# Decoding parameters
test_searcher: !name:speechbrain.decoders.CTCBeamSearcher
blank_index: 0
beam_size: 100
beam_prune_logp: -12.0
token_prune_min_logp: -1.2
prune_history: True
topk: 1
alpha: 1.0
beta: 0.5
# can be downloaded from here https://www.openslr.org/11/ or trained with kenLM
# It can either be a .bin or .arpa ; note: .arpa is much slower at loading
# If you don't want to use an LM, comment it out or set it to null
# kenlm_model_path: none


# AISHELL-1 has spaces between words in the transcripts,
# which Chinese writing normally does not do.
# If remove_spaces, spaces are removed
# from the transcript before computing CER.
remove_spaces: True
split_tokens: !apply:operator.not_ [!ref <remove_spaces>]

epoch_counter: !new:speechbrain.utils.epoch_loop.EpochCounter
   limit: !ref <number_of_epochs>

############################## Augmentations ###################################

# Speed perturbation
speed_perturb: !new:speechbrain.augment.time_domain.SpeedPerturb
   orig_freq: !ref <sample_rate>

# Time Drop
time_drop: !new:speechbrain.augment.freq_domain.SpectrogramDrop
   drop_length_low: 35
   drop_length_high: 45
   drop_count_low: 2
   drop_count_high: 2

# Frequency Drop
freq_drop: !new:speechbrain.augment.freq_domain.SpectrogramDrop
   drop_length_low: 25
   drop_length_high: 35
   drop_count_low: 2
   drop_count_high: 2
   dim: 2

# Time warp
time_warp: !new:speechbrain.augment.freq_domain.Warping

fea_augment: !new:speechbrain.augment.augmenter.Augmenter
   min_augmentations: 3
   max_augmentations: 3
   augment_prob: 1.0
   augmentations: [
      !ref <time_drop>,
      !ref <freq_drop>,
      !ref <time_warp>]

############################## Models ##########################################

enc: !new:speechbrain.nnet.containers.Sequential
   input_shape: [null, null, !ref <wav2vec_output_dim>]
   linear1: !name:speechbrain.nnet.linear.Linear
      n_neurons: !ref <dnn_neurons>
      bias: True
   bn1: !name:speechbrain.nnet.normalization.BatchNorm1d
   activation: !new:torch.nn.LeakyReLU
   drop: !new:torch.nn.Dropout
      p: !ref <dropout>
   linear2: !name:speechbrain.nnet.linear.Linear
      n_neurons: !ref <dnn_neurons>
      bias: True
   bn2: !name:speechbrain.nnet.normalization.BatchNorm1d
   activation2: !new:torch.nn.LeakyReLU
   drop2: !new:torch.nn.Dropout
      p: !ref <dropout>
   linear3: !name:speechbrain.nnet.linear.Linear
      n_neurons: !ref <dnn_neurons>
      bias: True
   bn3: !name:speechbrain.nnet.normalization.BatchNorm1d
   activation3: !new:torch.nn.LeakyReLU

wav2vec2: !new:speechbrain.lobes.models.huggingface_transformers.wav2vec2.Wav2Vec2
   source: !ref <wav2vec2_hub>
   output_norm: True
   freeze: !ref <freeze_wav2vec>
   save_path: !ref <wav2vec2_folder>

ctc_lin: !new:speechbrain.nnet.linear.Linear
   input_size: !ref <dnn_neurons>
   n_neurons: !ref <output_neurons>

log_softmax: !new:speechbrain.nnet.activations.Softmax
   apply_log: True

ctc_cost: !name:speechbrain.nnet.losses.ctc_loss
   blank_index: !ref <blank_index>

modules:
   wav2vec2: !ref <wav2vec2>
   enc: !ref <enc>
   ctc_lin: !ref <ctc_lin>

model: !new:torch.nn.ModuleList
   - [!ref <enc>, !ref <ctc_lin>]

model_opt_class: !name:torch.optim.Adadelta
   lr: !ref <lr>
   rho: 0.95
   eps: 1.e-8

wav2vec_opt_class: !name:torch.optim.Adam
   lr: !ref <lr_wav2vec>

lr_annealing_model: !new:speechbrain.nnet.schedulers.NewBobScheduler
   initial_value: !ref <lr>
   improvement_threshold: 0.0025
   annealing_factor: 0.8
   patient: 0

lr_annealing_wav2vec: !new:speechbrain.nnet.schedulers.NewBobScheduler
   initial_value: !ref <lr_wav2vec>
   improvement_threshold: 0.0025
   annealing_factor: 0.9
   patient: 0

############################## Logging and Pretrainer ##########################

checkpointer: !new:speechbrain.utils.checkpoints.Checkpointer
   checkpoints_dir: !ref <save_folder>
   recoverables:
      wav2vec2: !ref <wav2vec2>
      model: !ref <model>
      scheduler_model: !ref <lr_annealing_model>
      scheduler_wav2vec: !ref <lr_annealing_wav2vec>
      counter: !ref <epoch_counter>

train_logger: !new:speechbrain.utils.train_logger.FileTrainLogger
   save_file: !ref <train_log>

cer_computer: !name:speechbrain.utils.metric_stats.ErrorRateStats
   split_tokens: !ref <split_tokens>
