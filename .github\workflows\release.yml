# Automatically publish to PyPI when a GitHub release is published
name: Publish to PyPI

# Run this action when a release is published
on:  # yamllint disable-line rule:truthy
  release:
    types: [published]

# Updates version.txt, builds, and pushes to PyPI
jobs:
  release:
    name: Builds and publishes to PyPI
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          ref: main
      - uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Install pypa/build
        run: python -m pip install build --user
      - name: Build binary wheel and source tarball
        run: python -m build --sdist --wheel --outdir dist/
      - name: Publish to PyPI
        if: startsWith(github.ref, 'refs/tags')
        uses: pypa/gh-action-pypi-publish@master
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_KEY }}
