"""
Outil de benchmark pour l'extracteur de voix
Compare les performances entre différentes configurations
"""

import time
import json
import os
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
import pandas as pd
from config_advanced import get_preset_configs, get_config_summary
from extract_voice_improved import extract_voice

class VoiceBenchmark:
    def __init__(self, test_files_dir: str, output_base_dir: str = "benchmark_results"):
        self.test_files_dir = test_files_dir
        self.output_base_dir = output_base_dir
        self.results = []
        
        # Créer le dossier de résultats
        os.makedirs(output_base_dir, exist_ok=True)
    
    def run_benchmark(self, target_audio: str, negative_dir: str = None):
        """Lance le benchmark avec tous les presets"""
        presets = get_preset_configs()
        
        print("🚀 Démarrage du benchmark...")
        print(f"📁 Fichiers de test: {self.test_files_dir}")
        print(f"🎤 Audio cible: {target_audio}")
        print(f"🚫 Exclusions: {negative_dir or 'Aucune'}")
        print(f"⚙️ Presets à tester: {list(presets.keys())}")
        print("-" * 60)
        
        for preset_name, config in presets.items():
            print(f"\n🔧 Test du preset: {preset_name}")
            result = self._test_preset(preset_name, config, target_audio, negative_dir)
            self.results.append(result)
            
            # Affichage des résultats intermédiaires
            print(f"✅ {preset_name}: {result['segments_saved']} segments en {result['processing_time']:.2f}s")
        
        # Sauvegarder et analyser les résultats
        self._save_results()
        self._generate_report()
        
        return self.results
    
    def _test_preset(self, preset_name: str, config, target_audio: str, negative_dir: str):
        """Teste un preset spécifique"""
        # Créer un dossier de sortie pour ce preset
        output_dir = os.path.join(self.output_base_dir, f"preset_{preset_name}")
        os.makedirs(output_dir, exist_ok=True)
        
        # Mesurer le temps de traitement
        start_time = time.time()
        
        try:
            # Lancer l'extraction avec les paramètres du preset
            result_text = extract_voice(
                target_audio_path=target_audio,
                negative_audio_dir=negative_dir,
                input_path=self.test_files_dir,
                output_dir=output_dir,
                verification_threshold=config.similarity.verification_threshold,
                negative_threshold=config.similarity.negative_threshold,
                min_segment_duration=config.vad.min_speech_duration,
                vad_threshold=config.vad.simple_threshold,
                use_hystheresis=config.vad.use_hystheresis,
                activation_threshold=config.vad.activation_threshold,
                deactivation_threshold=config.vad.deactivation_threshold
            )
            
            processing_time = time.time() - start_time
            
            # Analyser les résultats
            stats = self._analyze_output(output_dir, result_text)
            
            return {
                'preset_name': preset_name,
                'processing_time': processing_time,
                'success': True,
                'error': None,
                'config': config,
                **stats
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                'preset_name': preset_name,
                'processing_time': processing_time,
                'success': False,
                'error': str(e),
                'segments_saved': 0,
                'total_duration': 0,
                'avg_similarity': 0,
                'files_processed': 0
            }
    
    def _analyze_output(self, output_dir: str, result_text: str):
        """Analyse les fichiers de sortie pour extraire les statistiques"""
        # Compter les fichiers WAV générés
        wav_files = list(Path(output_dir).glob("*.wav"))
        segments_saved = len(wav_files)
        
        # Analyser les noms de fichiers pour extraire les similarités
        similarities = []
        total_duration = 0
        
        for wav_file in wav_files:
            filename = wav_file.name
            # Extraire la similarité du nom de fichier (format: ...sim0.750.wav)
            if "_sim" in filename:
                try:
                    sim_part = filename.split("_sim")[1].split(".wav")[0]
                    similarity = float(sim_part)
                    similarities.append(similarity)
                except:
                    pass
            
            # Estimer la durée (approximation basée sur la taille du fichier)
            try:
                file_size = wav_file.stat().st_size
                # Approximation: 16kHz, 16-bit, mono = 32000 bytes/sec
                duration = file_size / 32000
                total_duration += duration
            except:
                pass
        
        # Chercher le fichier de statistiques JSON
        stats_files = list(Path(output_dir).glob("extraction_stats_*.json"))
        files_processed = 0
        if stats_files:
            try:
                with open(stats_files[0], 'r') as f:
                    stats_data = json.load(f)
                    files_processed = stats_data.get('files_processed', 0)
                    if 'total_duration_saved' in stats_data:
                        total_duration = stats_data['total_duration_saved']
            except:
                pass
        
        return {
            'segments_saved': segments_saved,
            'total_duration': total_duration,
            'avg_similarity': sum(similarities) / len(similarities) if similarities else 0,
            'min_similarity': min(similarities) if similarities else 0,
            'max_similarity': max(similarities) if similarities else 0,
            'files_processed': files_processed
        }
    
    def _save_results(self):
        """Sauvegarde les résultats du benchmark"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = os.path.join(self.output_base_dir, f"benchmark_results_{timestamp}.json")
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 Résultats sauvegardés: {results_file}")
    
    def _generate_report(self):
        """Génère un rapport de benchmark"""
        if not self.results:
            return
        
        # Créer un DataFrame pour l'analyse
        df_data = []
        for result in self.results:
            if result['success']:
                df_data.append({
                    'Preset': result['preset_name'],
                    'Temps (s)': result['processing_time'],
                    'Segments': result['segments_saved'],
                    'Durée totale (s)': result['total_duration'],
                    'Similarité moy.': result['avg_similarity'],
                    'Fichiers traités': result['files_processed']
                })
        
        if not df_data:
            print("❌ Aucun résultat valide pour générer le rapport")
            return
        
        df = pd.DataFrame(df_data)
        
        # Rapport texte
        report = f"""
📊 **RAPPORT DE BENCHMARK**
Généré le: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

🏆 **RÉSULTATS GLOBAUX:**
{df.to_string(index=False)}

🥇 **MEILLEURS PERFORMANCES:**
- Plus rapide: {df.loc[df['Temps (s)'].idxmin(), 'Preset']} ({df['Temps (s)'].min():.2f}s)
- Plus de segments: {df.loc[df['Segments'].idxmax(), 'Preset']} ({df['Segments'].max()} segments)
- Meilleure similarité: {df.loc[df['Similarité moy.'].idxmax(), 'Preset']} ({df['Similarité moy.'].max():.3f})
- Plus de durée: {df.loc[df['Durée totale (s)'].idxmax(), 'Preset']} ({df['Durée totale (s)'].max():.1f}s)

📈 **RECOMMANDATIONS:**
"""
        
        # Recommandations basées sur les résultats
        best_quality = df.loc[df['Similarité moy.'].idxmax(), 'Preset']
        best_quantity = df.loc[df['Segments'].idxmax(), 'Preset']
        best_speed = df.loc[df['Temps (s)'].idxmin(), 'Preset']
        
        report += f"""
- Pour la QUALITÉ: utilisez '{best_quality}'
- Pour la QUANTITÉ: utilisez '{best_quantity}'  
- Pour la VITESSE: utilisez '{best_speed}'
"""
        
        # Sauvegarder le rapport
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.output_base_dir, f"benchmark_report_{timestamp}.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        print(f"\n📋 Rapport complet sauvegardé: {report_file}")
        
        # Générer des graphiques si matplotlib est disponible
        try:
            self._generate_charts(df, timestamp)
        except ImportError:
            print("📊 Matplotlib non disponible - graphiques non générés")
    
    def _generate_charts(self, df, timestamp):
        """Génère des graphiques de performance"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Graphique 1: Temps de traitement
        ax1.bar(df['Preset'], df['Temps (s)'])
        ax1.set_title('Temps de traitement par preset')
        ax1.set_ylabel('Temps (secondes)')
        ax1.tick_params(axis='x', rotation=45)
        
        # Graphique 2: Nombre de segments
        ax2.bar(df['Preset'], df['Segments'])
        ax2.set_title('Nombre de segments extraits')
        ax2.set_ylabel('Segments')
        ax2.tick_params(axis='x', rotation=45)
        
        # Graphique 3: Similarité moyenne
        ax3.bar(df['Preset'], df['Similarité moy.'])
        ax3.set_title('Similarité moyenne')
        ax3.set_ylabel('Similarité')
        ax3.tick_params(axis='x', rotation=45)
        
        # Graphique 4: Durée totale extraite
        ax4.bar(df['Preset'], df['Durée totale (s)'])
        ax4.set_title('Durée totale extraite')
        ax4.set_ylabel('Durée (secondes)')
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        chart_file = os.path.join(self.output_base_dir, f"benchmark_charts_{timestamp}.png")
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 Graphiques sauvegardés: {chart_file}")

def main():
    """Fonction principale pour lancer le benchmark"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Benchmark de l'extracteur de voix")
    parser.add_argument("--target", required=True, help="Fichier audio cible")
    parser.add_argument("--input", required=True, help="Dossier de fichiers à tester")
    parser.add_argument("--negative", help="Dossier d'exclusions (optionnel)")
    parser.add_argument("--output", default="benchmark_results", help="Dossier de sortie")
    
    args = parser.parse_args()
    
    # Vérifications
    if not os.path.exists(args.target):
        print(f"❌ Fichier cible introuvable: {args.target}")
        return
    
    if not os.path.exists(args.input):
        print(f"❌ Dossier d'entrée introuvable: {args.input}")
        return
    
    # Lancer le benchmark
    benchmark = VoiceBenchmark(args.input, args.output)
    results = benchmark.run_benchmark(args.target, args.negative)
    
    print(f"\n🎯 Benchmark terminé! Résultats dans: {args.output}")

if __name__ == "__main__":
    main()
