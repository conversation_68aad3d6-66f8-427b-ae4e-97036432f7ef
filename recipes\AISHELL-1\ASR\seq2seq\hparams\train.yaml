# ############################################################################
# Model: E2E ASR with attention-based ASR
# Encoder: CRDNN model
# Decoder: GRU + beamsearch + RNNLM
# Tokens: BPE with unigram
# losses: CTC+ NLL
# Training: AISHELL-1
# Authors: <AUTHORS>
#           <PERSON><PERSON>, <PERSON><PERSON> 2021
# ############################################################################

seed: 1
__set_seed: !apply:torch.manual_seed [!ref <seed>]
output_folder: !ref results/base/<seed>
cer_file: !ref <output_folder>/cer.txt
save_folder: !ref <output_folder>/save
train_log: !ref <output_folder>/train_log.txt

# Data files
NOISE_DATASET_URL: https://www.dropbox.com/scl/fi/a09pj97s5ifan81dqhi4n/noises.zip?rlkey=j8b0n9kdjdr32o1f06t0cw5b7&dl=1
data_folder: !PLACEHOLDER # e,g./path/to/aishell
data_folder_noise: !ref <data_folder>/noise # The noisy sequences for data augmentation will automatically be downloaded here.
skip_prep: False
remove_compressed_wavs: False
ckpt_interval_minutes: 15 # save checkpoint every N min
train_data: !ref <output_folder>/train.csv
valid_data: !ref <output_folder>/dev.csv
test_data: !ref <output_folder>/test.csv
noise_annotation: !ref <save_folder>/noise.csv #The data manifest files are created by the data preparation script
tokenizer_file: speechbrain/asr-transformer-aishell/tokenizer.ckpt

####################### Training Parameters ####################################

number_of_epochs: 40
number_of_ctc_epochs: 10
batch_size: 16
lr: 0.0003
ctc_weight: 0.5
sorting: ascending
precision: fp32 # bf16, fp16 or fp32

dynamic_batching: True
max_batch_length: 15 # in terms of "duration" in annotations by default, second here
shuffle: False # if true re-creates batches at each epoch shuffling examples.
num_buckets: 10 # floor(log(max_batch_len/left_bucket_len, multiplier)) + 1
batch_ordering: ascending
dynamic_batch_sampler:
   max_batch_length: !ref <max_batch_length>
   shuffle: !ref <shuffle>
   num_buckets: !ref <num_buckets>
   batch_ordering: !ref <batch_ordering>

# Feature parameters
sample_rate: 16000
n_fft: 400
n_mels: 40

opt_class: !name:torch.optim.Adam
   lr: !ref <lr>

# Dataloader options
num_workers: 4
train_dataloader_opts:
   batch_size: !ref <batch_size>
   num_workers: !ref <num_workers>

valid_dataloader_opts:
   batch_size: !ref <batch_size>
   num_workers: !ref <num_workers>

test_dataloader_opts:
   batch_size: !ref <batch_size>
   num_workers: !ref <num_workers>

####################### Model Parameters #######################################
activation: !name:torch.nn.LeakyReLU
dropout: 0.15
cnn_blocks: 2
cnn_channels: (128, 256)
inter_layer_pooling_size: (2, 2)
cnn_kernelsize: (3, 3)
time_pooling_size: 4
rnn_class: !name:speechbrain.nnet.RNN.LSTM
rnn_layers: 4
rnn_neurons: 1024
rnn_bidirectional: True
dnn_blocks: 2
dnn_neurons: 512
emb_size: 128
dec_neurons: 1024
output_neurons: 5000  # Number of tokens
# we need to have blank_index != bos_index != eos_index when using CTCScorer
blank_index: 0
bos_index: 1
eos_index: 2
label_smoothing: 0.1

# Decoding parameters
min_decode_ratio: 0.0
max_decode_ratio: 1.0
beam_size: 80
eos_threshold: 1.5
using_max_attn_shift: True
max_attn_shift: 240
coverage_penalty: 1.5
temperature: 1.25
scorer_beam_scale: 0.5
# AISHELL-1 has spaces between words in the transcripts,
# which Chinese writing normally does not do.
# If remove_spaces, spaces are removed
# from the transcript before computing CER.
remove_spaces: True
split_tokens: !apply:operator.not_ [!ref <remove_spaces>]

epoch_counter: !new:speechbrain.utils.epoch_loop.EpochCounter
   limit: !ref <number_of_epochs>

normalize: !new:speechbrain.processing.features.InputNormalization
   norm_type: global

############################## Augmentations ###################################

compute_features: !new:speechbrain.lobes.features.Fbank
   sample_rate: !ref <sample_rate>
   n_fft: !ref <n_fft>
   n_mels: !ref <n_mels>

# Download and prepare the dataset of noisy sequences for augmentation
prepare_noise_data: !name:speechbrain.augment.preparation.prepare_dataset_from_URL
   URL: !ref <NOISE_DATASET_URL>
   dest_folder: !ref <data_folder_noise>
   ext: wav
   csv_file: !ref <noise_annotation>


# Add noise to input signal
add_noise: !new:speechbrain.augment.time_domain.AddNoise
   csv_file: !ref <noise_annotation>
   snr_low: 0
   snr_high: 15
   noise_sample_rate: !ref <sample_rate>
   clean_sample_rate: !ref <sample_rate>
   num_workers: !ref <num_workers>

# Speed perturbation
speed_perturb: !new:speechbrain.augment.time_domain.SpeedPerturb
   orig_freq: !ref <sample_rate>
   speeds: [95, 100, 105]

# Frequency drop: randomly drops a number of frequency bands to zero.
drop_freq: !new:speechbrain.augment.time_domain.DropFreq
   drop_freq_low: 0
   drop_freq_high: 1
   drop_freq_count_low: 1
   drop_freq_count_high: 3
   drop_freq_width: 0.05

# Time drop: randomly drops a number of temporal chunks.
drop_chunk: !new:speechbrain.augment.time_domain.DropChunk
   drop_length_low: 1000
   drop_length_high: 2000
   drop_count_low: 1
   drop_count_high: 5

# Augmenter: Combines previously defined augmentations to perform data augmentation
wav_augment: !new:speechbrain.augment.augmenter.Augmenter
   concat_original: True
   min_augmentations: 4
   max_augmentations: 4
   augment_prob: 1.0
   augmentations: [
      !ref <add_noise>,
      !ref <speed_perturb>,
      !ref <drop_freq>,
      !ref <drop_chunk>]

############################## Models ##########################################

enc: !new:speechbrain.lobes.models.CRDNN.CRDNN
   input_shape: [null, null, !ref <n_mels>]
   activation: !ref <activation>
   dropout: !ref <dropout>
   cnn_blocks: !ref <cnn_blocks>
   cnn_channels: !ref <cnn_channels>
   cnn_kernelsize: !ref <cnn_kernelsize>
   inter_layer_pooling_size: !ref <inter_layer_pooling_size>
   time_pooling: True
   using_2d_pooling: False
   time_pooling_size: !ref <time_pooling_size>
   rnn_class: !ref <rnn_class>
   rnn_layers: !ref <rnn_layers>
   rnn_neurons: !ref <rnn_neurons>
   rnn_bidirectional: !ref <rnn_bidirectional>
   rnn_re_init: True
   dnn_blocks: !ref <dnn_blocks>
   dnn_neurons: !ref <dnn_neurons>
   use_rnnp: False

emb: !new:speechbrain.nnet.embedding.Embedding
   num_embeddings: !ref <output_neurons>
   embedding_dim: !ref <emb_size>

dec: !new:speechbrain.nnet.RNN.AttentionalRNNDecoder
   enc_dim: !ref <dnn_neurons>
   input_size: !ref <emb_size>
   rnn_type: gru
   attn_type: location
   hidden_size: !ref <dec_neurons>
   attn_dim: 1024
   num_layers: 1
   scaling: 1.0
   channels: 10
   kernel_size: 100
   re_init: True
   dropout: !ref <dropout>

ctc_lin: !new:speechbrain.nnet.linear.Linear
   input_size: !ref <dnn_neurons>
   n_neurons: !ref <output_neurons>

seq_lin: !new:speechbrain.nnet.linear.Linear
   input_size: !ref <dec_neurons>
   n_neurons: !ref <output_neurons>

log_softmax: !new:speechbrain.nnet.activations.Softmax
   apply_log: True

ctc_cost: !name:speechbrain.nnet.losses.ctc_loss
   blank_index: !ref <blank_index>

seq_cost: !name:speechbrain.nnet.losses.nll_loss
   label_smoothing: !ref <label_smoothing>

# Models
modules:
   enc: !ref <enc>
   emb: !ref <emb>
   dec: !ref <dec>
   ctc_lin: !ref <ctc_lin>
   seq_lin: !ref <seq_lin>
   normalize: !ref <normalize>

model: !new:torch.nn.ModuleList
   - [!ref <enc>, !ref <emb>, !ref <dec>, !ref <ctc_lin>, !ref <seq_lin>]

tokenizer: !new:sentencepiece.SentencePieceProcessor

pretrainer: !new:speechbrain.utils.parameter_transfer.Pretrainer
   collect_in: !ref <save_folder>/tokenizer
   loadables:
      tokenizer: !ref <tokenizer>
   paths:
      tokenizer: !ref <tokenizer_file>

############################## Decoding ########################################

ctc_scorer: !new:speechbrain.decoders.scorer.CTCScorer
   eos_index: !ref <eos_index>
   blank_index: !ref <blank_index>
   ctc_fc: !ref <ctc_lin>

coverage_scorer: !new:speechbrain.decoders.scorer.CoverageScorer
   vocab_size: !ref <output_neurons>

scorer: !new:speechbrain.decoders.scorer.ScorerBuilder
   full_scorers: [!ref <coverage_scorer>, !ref <ctc_scorer>]
   weights:
      coverage: !ref <coverage_penalty>
      ctc: !ref <ctc_weight>
   scorer_beam_scale: !ref <scorer_beam_scale>

beam_search: !new:speechbrain.decoders.S2SRNNBeamSearcher
   embedding: !ref <emb>
   decoder: !ref <dec>
   linear: !ref <seq_lin>
   bos_index: !ref <bos_index>
   eos_index: !ref <eos_index>
   min_decode_ratio: !ref <min_decode_ratio>
   max_decode_ratio: !ref <max_decode_ratio>
   beam_size: !ref <beam_size>
   eos_threshold: !ref <eos_threshold>
   temperature: !ref <temperature>
   using_max_attn_shift: !ref <using_max_attn_shift>
   max_attn_shift: !ref <max_attn_shift>
   scorer: !ref <scorer>

lr_annealing: !new:speechbrain.nnet.schedulers.NewBobScheduler
   initial_value: !ref <lr>
   improvement_threshold: 0.0025
   annealing_factor: 0.8
   patient: 0

############################## Logging and Pretrainer ##########################

checkpointer: !new:speechbrain.utils.checkpoints.Checkpointer
   checkpoints_dir: !ref <save_folder>
   recoverables:
      model: !ref <model>
      scheduler: !ref <lr_annealing>
      normalizer: !ref <normalize>
      counter: !ref <epoch_counter>

train_logger: !new:speechbrain.utils.train_logger.FileTrainLogger
   save_file: !ref <train_log>

cer_computer: !name:speechbrain.utils.metric_stats.ErrorRateStats
   split_tokens: !ref <split_tokens>
