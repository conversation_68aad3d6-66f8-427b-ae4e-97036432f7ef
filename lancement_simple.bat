@echo off
title Extracteur de Voix PEPE v2.0

:menu
cls
echo.
echo ========================================
echo   EXTRACTEUR DE VOIX PEPE v2.0
echo ========================================
echo.
echo Choisissez votre version:
echo.
echo [1] Version classique (extract_voice.py)
echo [2] Version amelioree (extract_voice_improved.py) - RECOMMANDEE
echo [3] Configuration avancee
echo [4] Benchmark des presets
echo [5] Quitter
echo.

set /p choice="Votre choix (1-5): "

if "%choice%"=="1" goto version1
if "%choice%"=="2" goto version2
if "%choice%"=="3" goto config
if "%choice%"=="4" goto benchmark
if "%choice%"=="5" goto quit

echo Choix invalide. Veuillez choisir entre 1 et 5.
pause
goto menu

:version1
echo.
echo ===========================================
echo Lancement de la version classique...
echo ===========================================
cd /D H:\IA\speechbrain\song
call conda run -n H:\IA\speechbrain\venv python extract_voice.py
goto end

:version2
echo.
echo ===========================================
echo Lancement de la version amelioree...
echo ===========================================
cd /D H:\IA\speechbrain\song
call conda run -n H:\IA\speechbrain\venv python extract_voice_improved.py
goto end

:config
echo.
echo ===========================================
echo Creation des fichiers de configuration...
echo ===========================================
cd /D H:\IA\speechbrain\song
call conda run -n H:\IA\speechbrain\venv python config_advanced.py
echo.
echo Fichiers de configuration crees!
echo Consultez config_advanced.json et config_presets.json
pause
goto menu

:benchmark
echo.
echo ===========================================
echo Outil de benchmark
echo ===========================================
echo.
echo Le benchmark compare tous les presets sur vos fichiers.
echo.
echo Exemple d'utilisation:
echo python benchmark_tool.py --target CibleGohan.wav --input input --output benchmark_results
echo.
echo Voulez-vous voir l'aide complete? (o/n)
set /p showhelp="Votre choix: "
if /i "%showhelp%"=="o" (
    cd /D H:\IA\speechbrain\song
    call conda run -n H:\IA\speechbrain\venv python benchmark_tool.py --help
)
echo.
pause
goto menu

:quit
echo Au revoir!
exit /b 0

:end
echo.
echo ===========================================
echo Traitement termine!
echo ===========================================
pause
goto menu
