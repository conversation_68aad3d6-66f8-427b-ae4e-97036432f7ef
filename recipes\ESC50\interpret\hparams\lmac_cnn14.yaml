# #################################
# The recipe for training LMAC on the ESC50 dataset.
#
# Author:
#  * Francesco <PERSON> 2024
#  * Cem Subakan 2024
# #################################

# Seed needs to be set at top of yaml, before objects with parameters are made
seed: 1234
__set_seed: !!python/object/apply:torch.manual_seed [!ref <seed>]

# Set up folders for reading from and writing to
# Dataset must already exist at `audio_data_folder`
data_folder: /mnt/data/ESC50 # e.g., /localscratch/UrbanSound8K
audio_data_folder: !ref <data_folder>/audio

single_sample: null

int_method: "lmac"
overlap_type: "mixtures"

l_in_w: 4
l_out_w: 0.2
reg_w_tv: 0.0
reg_w_l1: 0.4

g_w: 4 # regularization weight of oracles
crosscor_th: 0.6
bin_th: 0.35 # needed to binarize guidance spectrograms
finetuning: False
guidelosstype: binary
crosscortype: 'dotp'

experiment_name: LMAC_cnn14
output_folder: !ref ./results/<experiment_name>/<seed>
save_folder: !ref <output_folder>/save
train_log: !ref <output_folder>/train_log.txt

wham_folder: null
wham_audio_folder: !ref <wham_folder>/tr

test_only: False
save_interpretations: True
interpret_period: 10

# Tensorboard logs
use_tensorboard: False
tensorboard_logs_folder: !ref <output_folder>/tb_logs/

# Path where data manifest files will be stored
train_annotation: !ref <data_folder>/manifest/train.json
valid_annotation: !ref <data_folder>/manifest/valid.json
test_annotation: !ref <data_folder>/manifest/test.json

# To standardize results, UrbanSound8k has pre-separated samples into
# 10 folds for multi-fold validation
train_fold_nums: [1, 2, 3]
valid_fold_nums: [4]
test_fold_nums: [5]
skip_manifest_creation: False


# this is needed for ood evaluation with ljspeech, when using eval.py
ljspeech_path: null

ckpt_interval_minutes: 15 # save checkpoint every N min

# Training parameters
number_of_epochs: 200
batch_size: 16
lr: 0.0002
sample_rate: 16000
use_mask_output: True
signal_length_s: 5
add_wham_noise: False


# Feature parameters
n_mels: 80

# Number of classes
out_n_neurons: 50

shuffle: True
dataloader_options:
    batch_size: !ref <batch_size>
    shuffle: !ref <shuffle>
    num_workers: 0

epoch_counter: !new:speechbrain.utils.epoch_loop.EpochCounter
    limit: !ref <number_of_epochs>

opt_class: !name:torch.optim.Adam
    lr: !ref <lr>
    weight_decay: 0.000002

lr_annealing: !new:speechbrain.nnet.schedulers.ReduceLROnPlateau
    factor: 0.5
    patience: 3
    dont_halve_until_epoch: 100

# Logging + checkpoints
train_logger: !new:speechbrain.utils.train_logger.FileTrainLogger
    save_file: !ref <train_log>

checkpointer: !new:speechbrain.utils.checkpoints.Checkpointer
    checkpoints_dir: !ref <save_folder>
    recoverables:
        psi_model: !ref <psi_model>
        counter: !ref <epoch_counter>


return_reps: True
embedding_model: !new:speechbrain.lobes.models.Cnn14.Cnn14
    mel_bins: !ref <n_mels>
    emb_dim: 2048
    return_reps: !ref <return_reps>

classifier: !new:torch.nn.Linear
    in_features: 2048
    out_features: !ref <out_n_neurons>


# this is needed when using eval.py to specify the path for the psi_model.ckpt
# the typical path would be similar to results/LMAC_cnn14/1234/save/CKPT+2024-06-20+16-05-44+00/psi_model.ckpt
pretrained_interpreter: null

load_pretrained: !new:speechbrain.utils.parameter_transfer.Pretrainer
    collect_in: !ref <save_folder>
    loadables:
        psi: !ref <psi_model>
    paths:
        psi: !ref <pretrained_interpreter>

use_pretrained: True
embedding_model_path: fpaissan/r/embedding_model.ckpt
classifier_model_path: fpaissan/r/classifier.ckpt

pretrained_esc50: !new:speechbrain.utils.parameter_transfer.Pretrainer
    collect_in: !ref <save_folder>
    loadables:
        embedding_model: !ref <embedding_model>
        classifier: !ref <classifier>
    paths:
        embedding_model: !ref <embedding_model_path>
        classifier: !ref <classifier_model_path>

# pre-processing
n_fft: 1024
hop_length: 11.6099
win_length: 23.2199
use_melspectra_log1p: True
use_stft2mel: True

compute_stft: !new:speechbrain.processing.features.STFT
    n_fft: !ref <n_fft>
    hop_length: !ref <hop_length>
    win_length: !ref <win_length>
    sample_rate: !ref <sample_rate>

compute_fbank: !new:speechbrain.processing.features.Filterbank
    n_mels: !ref <n_mels>
    n_fft: !ref <n_fft>
    sample_rate: !ref <sample_rate>
    log_mel: False

compute_istft: !new:speechbrain.processing.features.ISTFT
    sample_rate: !ref <sample_rate>
    hop_length: !ref <hop_length>
    win_length: !ref <win_length>

psi_model: !new:speechbrain.lobes.models.Cnn14.CNN14PSI_stft
    dim: 2048

modules:
    compute_stft: !ref <compute_stft>
    compute_fbank: !ref <compute_fbank>
    compute_istft: !ref <compute_istft>
    psi: !ref <psi_model>
