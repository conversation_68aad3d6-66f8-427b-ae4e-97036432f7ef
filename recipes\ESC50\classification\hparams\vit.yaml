# #################################
# Basic training parameters for sound classification using the ESC50 dataset.
# This recipe uses a ViT backbone for classification.
#
# Authors: <AUTHORS>
#  * Francesco <PERSON> 2022, 2023
#  * Luca Della Libera 2024
#  (based on the SpeechBrain UrbanSound8k recipe)
# #################################

# Seed needs to be set at top of yaml, before objects with parameters are made
seed: 1234
__set_seed: !!python/object/apply:torch.manual_seed [!ref <seed>]

# Set up folders for reading from and writing to
data_folder: !PLACEHOLDER  # e.g., /localscratch/ESC-50-master
audio_data_folder: !ref <data_folder>/audio

experiment_name: vit-base-esc50
output_folder: !ref ./results/<experiment_name>/<seed>
save_folder: !ref <output_folder>/save
train_log: !ref <output_folder>/train_log.txt

add_wham_noise: False
use_melspectra: False
use_log1p_mel: False
test_only: False

wham_folder: null # Set it if add_wham_noise is True
wham_audio_folder: !ref <wham_folder>/tr

# Tensorboard logs
use_tensorboard: False
tensorboard_logs_folder: !ref <output_folder>/tb_logs/

# Path where data manifest files will be stored
train_annotation: !ref <data_folder>/manifest/train.json
valid_annotation: !ref <data_folder>/manifest/valid.json
test_annotation: !ref <data_folder>/manifest/test.json

# To standardize results, UrbanSound8k has pre-separated samples into
# 10 folds for multi-fold validation
train_fold_nums: [1, 2, 3]
valid_fold_nums: [4]
test_fold_nums: [5]
skip_manifest_creation: False

ckpt_interval_minutes: 15 # save checkpoint every N min

# Training parameters
number_of_epochs: 100
batch_size: 16
lr: 0.0002
base_lr: 0.00000001
max_lr: !ref <lr>
step_size: 65000

sample_rate: 16000
signal_length_s: 5

# Number of classes
out_n_neurons: 50

# Note that it's actually important to shuffle the data here
shuffle: True
dataloader_options:
    batch_size: !ref <batch_size>
    shuffle: !ref <shuffle>
    num_workers: 0

# Augmentation
drop_freq: !new:speechbrain.augment.time_domain.DropFreq
    drop_freq_low: 0  # Min frequency band dropout probability
    drop_freq_high: 1  # Max frequency band dropout probability
    drop_freq_count_low: 1  # Min number of frequency bands to drop
    drop_freq_count_high: 3  # Max number of frequency bands to drop
    drop_freq_width: 0.05  # Width of frequency bands to drop

drop_chunk: !new:speechbrain.augment.time_domain.DropChunk
    drop_length_low: 1  # Min number of audio chunks to drop
    drop_length_high: 5  # Max number of audio chunks to drop
    drop_count_low: 1000  # Min length of audio chunks to drop
    drop_count_high: 2000  # Max length of audio chunks to drop

augmentation: !new:speechbrain.augment.augmenter.Augmenter
    parallel_augment: False
    concat_original: False
    repeat_augment: 1
    shuffle_augmentations: False
    min_augmentations: 2
    max_augmentations: 2
    augment_prob: 0.75
    augmentations: [!ref <drop_freq>, !ref <drop_chunk>]

# Model
embedding_model: !apply:transformers.ViTModel.from_pretrained [google/vit-base-patch16-224]

classifier: !new:speechbrain.lobes.models.ECAPA_TDNN.Classifier
    input_size: 768
    out_neurons: !ref <out_n_neurons>
    lin_blocks: 1

epoch_counter: !new:speechbrain.utils.epoch_loop.EpochCounter
    limit: !ref <number_of_epochs>

# Pre-processing
n_fft: 1024
spec_mag_power: 0.5
hop_length: 11.6099
win_length: 23.2199

compute_stft: !new:speechbrain.processing.features.STFT
    n_fft: !ref <n_fft>
    hop_length: !ref <hop_length>
    win_length: !ref <win_length>
    sample_rate: !ref <sample_rate>

modules:
    compute_stft: !ref <compute_stft>
    embedding_model: !ref <embedding_model>
    classifier: !ref <classifier>

compute_cost: !new:speechbrain.nnet.losses.LogSoftmaxWrapper
    loss_fn: !new:speechbrain.nnet.losses.AdditiveAngularMargin
        margin: 0.2
        scale: 30

opt_class: !name:torch.optim.Adam
    lr: !ref <lr>
    weight_decay: 0.000002

lr_annealing: !new:speechbrain.nnet.schedulers.CyclicLRScheduler
    base_lr: !ref <base_lr>
    max_lr: !ref <max_lr>
    step_size: !ref <step_size>

# Logging + checkpoints
train_logger: !new:speechbrain.utils.train_logger.FileTrainLogger
    save_file: !ref <train_log>

error_stats: !name:speechbrain.utils.metric_stats.MetricStats
    metric: !name:speechbrain.nnet.losses.classification_error
        reduction: batch

checkpointer: !new:speechbrain.utils.checkpoints.Checkpointer
    checkpoints_dir: !ref <save_folder>
    recoverables:
        embedding_model: !ref <embedding_model>
        classifier: !ref <classifier>
        counter: !ref <epoch_counter>
