# ############################################################################
# Model: E2E ST with Transformer
# Encoder: Transformer Encoder
# Decoder: Transformer Decoder beamsearch
# Tokens: BPE
# losses: CTC + KLdiv (Label Smoothing loss)
# Training: Fisher-Callhome
# Authors: <AUTHORS>
# ############################################################################
# Seed needs to be set at top of yaml, before objects with parameters are made
# The original recipe is from ESPnet:
# https://github.com/espnet/espnet/blob/master/egs/fisher_callhome_spanish/st1/conf/tuning/train_pytorch_transformer.yaml

debug: False
seed: 8886
num_workers: 8
__set_seed: !apply:torch.manual_seed [!ref <seed>]
output_folder: !ref results/transformer/<seed>
ckpt_interval_minutes: 15 # save checkpoint every N min
bleu_file: !ref <output_folder>/bleu.txt
save_folder: !ref <output_folder>/save
train_log: !ref <output_folder>/train_log.txt

# Data files
data_folder: !PLACEHOLDER # Folder of the files generated by the preparation script
tokenizer_file: !PLACEHOLDER # .model file corresponding to the Tokenizer model

# Tokenier initialization
tokenizer: !new:sentencepiece.SentencePieceProcessor

# Pretrain the tokenizer
pretrainer: !new:speechbrain.utils.parameter_transfer.Pretrainer
    collect_in: ./tokenizer
    loadables:
        tokenizer: !ref <tokenizer>
    paths:
        tokenizer: !ref <tokenizer_file>

# The train logger writes training statistics to a file, as well as stdout.
train_logger: !new:speechbrain.utils.train_logger.FileTrainLogger
    save_file: !ref <train_log>

# Features
sample_rate: 16000
n_fft: 400
n_mels: 80

compute_features: !new:speechbrain.lobes.features.Fbank
    sample_rate: !ref <sample_rate>
    n_fft: !ref <n_fft>
    n_mels: !ref <n_mels>

normalize: !new:speechbrain.processing.features.InputNormalization
    norm_type: global
    update_until_epoch: 4

# Speed perturbation
speed_changes: [90, 100, 110]  # List of speed changes for time-stretching

speed_perturb: !new:speechbrain.augment.time_domain.SpeedPerturb
    orig_freq: !ref <sample_rate>
    speeds: !ref <speed_changes>

# Trainer settings
number_of_epochs: 50
valid_search_epoch: 10
batch_size: 8 # this works for 2 GPUs with 11GB
grad_accumulation_factor: 8
loss_reduction: batchmean
sorting: random
avg_checkpoints: 5 # Number of checkpoints to average for evaluation

# stages related parameters
stage_one_epochs: 100 # not gonna changing optimizer in this recipe
lr_adam: 2.5
lr_sgd: 0.000025

epoch_counter: !new:speechbrain.utils.epoch_loop.EpochCounter
    limit: !ref <number_of_epochs>

# Dataloader options
train_dataloader_opts:
    batch_size: !ref <batch_size>
    num_workers: !ref <num_workers>

valid_dataloader_opts:
    batch_size: !ref <batch_size>
    num_workers: !ref <num_workers>

test_dataloader_opts:
    batch_size: !ref <batch_size>
    num_workers: !ref <num_workers>

####################### Model Parameters ###########################
# Transformer
d_model: 256
nhead: 4
num_encoder_layers: 12
num_decoder_layers: 6
d_ffn: 2048
transformer_dropout: 0.1
activation: !name:torch.nn.GELU
output_neurons: 1000
vocab_size: 1000
attention_type: "regularMHA" # "RelPosMHAXL" or "regularMHA"

# Multi-task
# ctc_weight controls the type of auxiliary ASR decoder
# ctc_weight: 1 means only CTC Loss is applied to ASR task
# ctc_weight: 0 means only Attention Loss is applied to ASR task
# ctc_weight: between 0 and 1 means hybrid CTC and Attention

# asr_weight controls the degree of the auxiliary ASR task, which contributes to loss
# mt_weight controls the degree of the auxiliary MT task, which contribute to loss
# ST Loss =
# (1 - asr_weight - mt_weight) * ST Attention Loss +
# asr_weight * ASR Loss +
# mt_weight * MT Loss

# don't forget to uncomment the ctc_lin in modules section (line:185) when using ctc
ctc_weight: 0
asr_weight: 0
mt_weight: 0

# Outputs
blank_index: 0
label_smoothing: 0.1
pad_index: 0
bos_index: 1
eos_index: 2

# Decoding parameters
min_decode_ratio: 0.0
max_decode_ratio: 1.0
valid_search_interval: !ref <valid_search_epoch>
valid_beam_size: 10
test_beam_size: 10

############################## models ################################
CNN: !new:speechbrain.lobes.models.convolution.ConvolutionFrontEnd
    input_shape: (8, 10, 80)
    num_blocks: 2
    num_layers_per_block: 1
    out_channels: (256, 256)
    kernel_sizes: (3, 3)
    strides: (2, 2)
    residuals: (False, False)

Transformer: !new:speechbrain.lobes.models.transformer.TransformerST.TransformerST # yamllint disable-line rule:line-length
    input_size: 5120
    tgt_vocab: !ref <output_neurons>
    d_model: !ref <d_model>
    nhead: !ref <nhead>
    num_encoder_layers: !ref <num_encoder_layers>
    num_decoder_layers: !ref <num_decoder_layers>
    d_ffn: !ref <d_ffn>
    dropout: !ref <transformer_dropout>
    activation: !ref <activation>
    ctc_weight: !ref <ctc_weight>
    asr_weight: !ref <asr_weight>
    mt_weight: !ref <mt_weight>
    asr_tgt_vocab: !ref <output_neurons>
    mt_src_vocab: !ref <output_neurons>
    attention_type: !ref <attention_type>
    normalize_before: True
    causal: False

# only when multi-task setting is used
ctc_lin: !new:speechbrain.nnet.linear.Linear
    input_size: !ref <d_model>
    n_neurons: !ref <output_neurons>

seq_lin: !new:speechbrain.nnet.linear.Linear
    input_size: !ref <d_model>
    n_neurons: !ref <output_neurons>

# when asr-weight > 0 and ctc-weight < 1
asr_seq_lin: !new:speechbrain.nnet.linear.Linear
    input_size: !ref <d_model>
    n_neurons: !ref <vocab_size>

modules:
    CNN: !ref <CNN>
    Transformer: !ref <Transformer>
    seq_lin: !ref <seq_lin>
    asr_seq_lin: !ref <asr_seq_lin>
    ctc_lin: !ref <ctc_lin>

model: !new:torch.nn.ModuleList
    - [!ref <CNN>, !ref <Transformer>, !ref <seq_lin>]

# define two optimizers here for two-stage training
Adam: !name:torch.optim.Adam
    lr: 0
    betas: (0.9, 0.98)
    eps: 0.000000001

SGD: !name:torch.optim.SGD
    lr: !ref <lr_sgd>
    momentum: 0.99
    nesterov: True

valid_search: !new:speechbrain.decoders.S2STransformerBeamSearcher
    modules: [!ref <Transformer>, !ref <seq_lin>]
    bos_index: !ref <bos_index>
    eos_index: !ref <eos_index>
    min_decode_ratio: !ref <min_decode_ratio>
    max_decode_ratio: !ref <max_decode_ratio>
    beam_size: !ref <valid_beam_size>
    using_eos_threshold: False
    length_normalization: True

test_search: !new:speechbrain.decoders.S2STransformerBeamSearcher
    modules: [!ref <Transformer>, !ref <seq_lin>]
    bos_index: !ref <bos_index>
    eos_index: !ref <eos_index>
    min_decode_ratio: !ref <min_decode_ratio>
    max_decode_ratio: !ref <max_decode_ratio>
    beam_size: !ref <test_beam_size>
    using_eos_threshold: True
    length_normalization: True

log_softmax: !new:torch.nn.LogSoftmax
    dim: -1

ctc_cost: !name:speechbrain.nnet.losses.ctc_loss
    blank_index: !ref <blank_index>
    reduction: !ref <loss_reduction>

seq_cost: !name:speechbrain.nnet.losses.kldiv_loss
    label_smoothing: !ref <label_smoothing>
    reduction: !ref <loss_reduction>

noam_annealing: !new:speechbrain.nnet.schedulers.NoamScheduler
    lr_initial: !ref <lr_adam>
    n_warmup_steps: 25000
    model_size: !ref <d_model>

# Checkpoint setting
checkpointer: !new:speechbrain.utils.checkpoints.Checkpointer
    checkpoints_dir: !ref <save_folder>
    recoverables:
        model: !ref <model>
        noam_scheduler: !ref <noam_annealing>
        normalizer: !ref <normalize>
        counter: !ref <epoch_counter>

bleu_computer: !name:speechbrain.utils.bleu.BLEUStats
    merge_words: False
acc_computer: !name:speechbrain.utils.Accuracy.AccuracyStats
